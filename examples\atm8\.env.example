# Minecraft Server Configuration
MINECRAFT_VERSION=1.21.4
MINECRAFT_MEMORY=4G
MINECRAFT_ONLINE_MODE=FALSE
MINECRAFT_DIFFICULTY=easy
MINECRAFT_GAMEMODE=survival
MINECRAFT_LEVEL_NAME=world
MINECRAFT_SEED=
MINECRAFT_MOTD=A Minecraft Server managed by Docker

# RCON Configuration
RCON_PASSWORD=minecraft
RCON_PORT=25575

# Web Application Configuration
FLASK_SECRET_KEY=your-super-secret-key-change-this
FLASK_ENV=production
WEBAPP_PORT=5000

# File Browser Configuration
FILEBROWSER_PORT=25580

# Network Configuration
MINECRAFT_PORT=25565

# SSL Configuration (for nginx)
SSL_CERT_PATH=./ssl/cert.pem
SSL_KEY_PATH=./ssl/key.pem

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_INTERVAL=24h
BACKUP_RETENTION=7
