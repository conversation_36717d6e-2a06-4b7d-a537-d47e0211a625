#!/bin/bash

# Fix Online Mode for Cracked Accounts
# This script restarts the server with online-mode=false to allow cracked accounts

echo "🔧 Fixing Online Mode for Cracked Account Support..."
echo "=================================================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }

# Check Docker
if ! docker info &> /dev/null; then
    echo -e "${RED}❌ Docker not running!${NC}"
    exit 1
fi

log_info "Stopping Minecraft server to apply online-mode fix..."
docker-compose stop mc

log_info "Updating server configuration..."
# The docker-compose.yml has already been updated to ONLINE_MODE: "false"

log_info "Starting Minecraft server with cracked account support..."
docker-compose up -d mc

log_info "Waiting for server to start..."
for i in {1..30}; do
    if docker-compose logs mc | grep -q "Done\|Server startup"; then
        break
    fi
    echo "   Server starting... ($i/30)"
    sleep 2
done

log_success "Server restarted with cracked account support!"

echo ""
echo "🎮 Server Configuration:"
echo "========================"
echo "✅ Online Mode: DISABLED (allows cracked accounts)"
echo "✅ AuthMe: Handles all authentication"
echo "✅ Server Address: localhost:25565"
echo ""
echo "📝 Now you can connect with any username!"
echo "========================"
echo "1. Open Minecraft Java Edition"
echo "2. Go to Multiplayer"
echo "3. Add Server: localhost:25565"
echo "4. Join with any username (like 'abusaker')"
echo "5. Register: /register password password"
echo ""

log_info "Showing recent server logs..."
docker-compose logs --tail=10 mc

echo ""
log_success "Ready to connect! Try joining again with your username."
