{% extends "base.html" %}

{% block title %}Console - Minecraft Server Manager{% endblock %}

{% block content %}
<!-- Console Header Stats -->
<div class="row g-4 mb-4">
    <div class="col-md-3">
        <div class="card glass-intense animate__animated animate__fadeInUp">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-opacity-20 rounded-circle p-3 position-relative">
                            <i class="fas fa-circle text-success fs-4 animate__animated animate__pulse animate__infinite"></i>
                            <div class="position-absolute top-0 start-0 w-100 h-100 rounded-circle" style="background: radial-gradient(circle, rgba(16, 185, 129, 0.2) 0%, transparent 70%); animation: pulse 2s infinite;"></div>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="mb-1 text-white">Connection Status</h6>
                        <p class="mb-0 text-white-50">Real-time monitoring</p>
                    </div>
                </div>
                <span class="badge bg-success">
                    <i class="fas fa-wifi me-1"></i>CONNECTED
                </span>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card glass-intense animate__animated animate__fadeInUp animate__delay-1s">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-opacity-20 rounded-circle p-3 position-relative">
                            <i class="fas fa-sync-alt text-primary fs-4" id="refreshStatusIcon"></i>
                            <div class="position-absolute top-0 start-0 w-100 h-100 rounded-circle" style="background: radial-gradient(circle, rgba(99, 102, 241, 0.2) 0%, transparent 70%); animation: pulse 2s infinite 0.5s;"></div>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="mb-1 text-white">Auto-Refresh</h6>
                        <p class="mb-0 text-white-50">Every 5 seconds</p>
                    </div>
                </div>
                <span class="badge bg-secondary" id="autoRefreshBadge">
                    <i class="fas fa-pause me-1"></i>DISABLED
                </span>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card glass-intense animate__animated animate__fadeInUp animate__delay-2s">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0">
                        <div class="bg-info bg-opacity-20 rounded-circle p-3 position-relative">
                            <i class="fas fa-list text-info fs-4"></i>
                            <div class="position-absolute top-0 start-0 w-100 h-100 rounded-circle" style="background: radial-gradient(circle, rgba(6, 182, 212, 0.2) 0%, transparent 70%); animation: pulse 2s infinite 1s;"></div>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="mb-1 text-white">Log Lines</h6>
                        <p class="mb-0 text-white-50">Current buffer</p>
                    </div>
                </div>
                <span class="badge bg-info">
                    <i class="fas fa-file-alt me-1"></i>100 LINES
                </span>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card glass-intense animate__animated animate__fadeInUp animate__delay-3s">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-opacity-20 rounded-circle p-3 position-relative">
                            <i class="fas fa-clock text-warning fs-4"></i>
                            <div class="position-absolute top-0 start-0 w-100 h-100 rounded-circle" style="background: radial-gradient(circle, rgba(245, 158, 11, 0.2) 0%, transparent 70%); animation: pulse 2s infinite 1.5s;"></div>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="mb-1 text-white">Last Updated</h6>
                        <p class="mb-0 text-white-50">Timestamp</p>
                    </div>
                </div>
                <span class="badge bg-warning" id="lastUpdatedBadge">
                    <i class="fas fa-clock me-1"></i>NOW
                </span>
            </div>
        </div>
    </div>
</div>

<!-- Main Console -->
<div class="row g-4">
    <div class="col-12">
        <div class="card glass-intense animate__animated animate__fadeInUp animate__delay-4s">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-terminal me-2 text-accent-glass"></i>Server Console
                    <span class="badge bg-success ms-2 animate__animated animate__pulse animate__infinite" style="font-size: 0.7rem;">LIVE</span>
                </h5>
                <div class="d-flex gap-2">
                    <button class="btn btn-sm btn-outline-primary glass-btn" onclick="refreshLogs()" data-bs-toggle="tooltip" title="Refresh logs">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-secondary glass-btn" onclick="toggleAutoRefresh()" data-bs-toggle="tooltip" title="Toggle auto-refresh">
                        <i class="fas fa-play" id="autoRefreshIcon"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-info glass-btn" onclick="clearConsole()" data-bs-toggle="tooltip" title="Clear console">
                        <i class="fas fa-trash"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-success glass-btn" onclick="downloadLogs()" data-bs-toggle="tooltip" title="Download logs">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-warning glass-btn" onclick="toggleFullscreen()" data-bs-toggle="tooltip" title="Toggle fullscreen">
                        <i class="fas fa-expand" id="fullscreenIcon"></i>
                    </button>
                </div>
            </div>
            <div class="card-body p-0 position-relative">
                <div class="enhanced-console-output" id="consoleOutput">
                    <div class="console-content pt-4">
                        {{ logs if logs else "Loading console logs..." }}
                    </div>
                </div>
                <div class="position-absolute top-0 end-0 p-3" style="z-index: 10;">
                    <div class="d-flex align-items-center glass rounded-pill px-3 py-2 border border-success border-opacity-25">
                        <i class="fas fa-circle text-success me-2 animate__animated animate__pulse animate__infinite" style="font-size: 0.5rem;"></i>
                        <small class="text-white fw-medium">Live Console</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}