#!/bin/bash

# Complete Reset and Rebuild Script for Minecraft Server
# This script removes all old containers, images, and volumes, then rebuilds everything fresh

set -e

echo "🔄 Complete Reset and Rebuild of Minecraft Server..."
echo "===================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

step_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

step_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

step_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

step_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Docker is running
echo "🐳 Checking Docker status..."
if ! docker info &> /dev/null; then
    step_error "Docker is not running. Please start Docker Desktop first."
    exit 1
fi
step_success "Docker is running"

# Step 1: Stop and remove all containers
echo ""
echo "🛑 Step 1: Stopping and removing containers..."
step_info "Stopping all containers for this project..."

# Stop containers if they exist
if docker-compose ps -q | grep -q .; then
    docker-compose down --remove-orphans
    step_success "Containers stopped and removed"
else
    step_info "No running containers found"
fi

# Remove any orphaned containers
step_info "Removing any orphaned containers..."
docker container prune -f
step_success "Orphaned containers removed"

# Step 2: Remove volumes (this will delete all data!)
echo ""
echo "🗄️  Step 2: Removing volumes..."
step_warning "This will delete ALL server data including player data and database!"
step_info "Proceeding with automatic cleanup for fresh setup..."

# Remove project volumes
step_info "Removing project volumes..."
docker-compose down -v
docker volume prune -f
step_success "Volumes removed"

# Step 3: Remove images
echo ""
echo "🖼️  Step 3: Removing old images..."
step_info "Removing Minecraft server images..."

# Remove specific images
docker rmi itzg/minecraft-server:latest 2>/dev/null || true
docker rmi mariadb:10.11 2>/dev/null || true

# Clean up unused images
docker image prune -f
step_success "Old images removed"

# Step 4: Clean up networks
echo ""
echo "🌐 Step 4: Cleaning up networks..."
docker network prune -f
step_success "Unused networks removed"

# Step 5: Pull fresh images
echo ""
echo "📥 Step 5: Pulling fresh images..."
step_info "Pulling latest Minecraft server image..."
docker pull itzg/minecraft-server:latest

step_info "Pulling latest MariaDB image..."
docker pull mariadb:10.11

step_success "Fresh images pulled"

# Step 6: Recreate directory structure
echo ""
echo "📁 Step 6: Recreating directory structure..."
step_info "Creating fresh directory structure..."

# Remove old plugin configs and recreate
rm -rf plugins/ config/ data/ 2>/dev/null || true

mkdir -p plugins/AuthMe
mkdir -p plugins/Essentials  
mkdir -p plugins/WelcomeMessages
mkdir -p config
mkdir -p data

step_success "Directory structure created"

# Step 7: Restore configuration files
echo ""
echo "⚙️  Step 7: Ensuring configuration files exist..."

# Check if config files exist, if not, recreate them
if [ ! -f "plugins/AuthMe/config.yml" ]; then
    step_warning "AuthMe config missing - you may need to recreate it"
fi

if [ ! -f "plugins/Essentials/config.yml" ]; then
    step_warning "Essentials config missing - you may need to recreate it"
fi

if [ ! -f "plugins/WelcomeMessages/config.yml" ]; then
    step_warning "WelcomeMessages config missing - you may need to recreate it"
fi

if [ ! -f "config/init-db.sql" ]; then
    step_warning "Database init script missing - you may need to recreate it"
fi

step_success "Configuration check complete"

# Step 8: Build and start fresh containers
echo ""
echo "🏗️  Step 8: Building and starting fresh containers..."
step_info "Starting database container..."
docker-compose up -d db

# Wait for database to be ready
step_info "Waiting for database to initialize..."
timeout=60
counter=0
while ! docker-compose exec -T db mysqladmin ping -h localhost -u root -pminecraft_root_pass --silent 2>/dev/null; do
    if [ $counter -eq $timeout ]; then
        step_error "Database failed to start within $timeout seconds"
        docker-compose logs db
        exit 1
    fi
    if [ $((counter % 10)) -eq 0 ]; then
        echo "   Database initializing... ($counter/$timeout)"
    fi
    sleep 2
    counter=$((counter + 1))
done

step_success "Database is ready"

step_info "Starting Minecraft server..."
docker-compose up -d mc

# Wait for Minecraft server to start
step_info "Waiting for Minecraft server to start..."
timeout=180
counter=0
while ! docker-compose logs mc | grep -q "Done\|Timings Reset"; do
    if [ $counter -eq $timeout ]; then
        step_error "Minecraft server failed to start within $timeout seconds"
        echo "📋 Server logs:"
        docker-compose logs mc
        exit 1
    fi
    if [ $((counter % 15)) -eq 0 ]; then
        echo "   Server starting... ($counter/$timeout)"
    fi
    sleep 3
    counter=$((counter + 3))
done

step_success "Minecraft server is ready"

# Step 9: Verify everything is working
echo ""
echo "✅ Step 9: Verifying setup..."
step_info "Checking container status..."
docker-compose ps

step_info "Checking if plugins are loading..."
sleep 5
if docker-compose logs mc | grep -q "AuthMe"; then
    step_success "AuthMe plugin detected"
else
    step_warning "AuthMe plugin not detected yet"
fi

if docker-compose logs mc | grep -q "Essentials"; then
    step_success "Essentials plugin detected"
else
    step_warning "Essentials plugin not detected yet"
fi

# Final status
echo ""
echo "🎉 Reset and Rebuild Complete!"
echo "============================================="
echo "🌐 Server Address: localhost:25565"
echo "🎮 Minecraft Version: 1.21.4"
echo "🔧 Server Type: Paper"
echo "🗄️  Database: MariaDB (fresh)"
echo ""
echo "📝 Player Commands:"
echo "  New Players: /register <password> <confirmPassword>"
echo "  Returning:   /login <password>"
echo ""
echo "🔧 Management Commands:"
echo "  View logs:   docker-compose logs -f mc"
echo "  Stop server: docker-compose down"
echo "  Restart:     docker-compose restart mc"
echo ""
echo "📋 Next Steps:"
echo "1. Test connection to localhost:25565"
echo "2. Try registering a new account"
echo "3. Check that welcome messages appear"
echo "4. Verify authentication works correctly"
echo ""

# Ask if user wants to follow logs
read -p "📋 Would you like to follow the server logs now? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "📋 Following server logs (Press Ctrl+C to exit):"
    docker-compose logs -f mc
fi
