#!/bin/bash

# Test Script for Minecraft Authentication Setup
# This script validates that all components are working correctly

set -e

echo "🧪 Testing Minecraft Authentication Setup..."
echo "============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test functions
test_passed() {
    echo -e "${GREEN}✅ $1${NC}"
}

test_failed() {
    echo -e "${RED}❌ $1${NC}"
    return 1
}

test_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

test_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Test 1: Check if Docker is running
echo "🐳 Testing Docker..."
if docker info &> /dev/null; then
    test_passed "Docker is running"
else
    test_failed "Docker is not running or accessible"
    exit 1
fi

# Test 2: Check if docker-compose.yml exists
echo ""
echo "📄 Testing configuration files..."
if [ -f "docker-compose.yml" ]; then
    test_passed "docker-compose.yml exists"
else
    test_failed "docker-compose.yml not found"
    exit 1
fi

# Test 3: Check plugin configurations
if [ -f "plugins/AuthMe/config.yml" ]; then
    test_passed "AuthMe configuration exists"
else
    test_failed "AuthMe configuration missing"
fi

if [ -f "plugins/Essentials/config.yml" ]; then
    test_passed "Essentials configuration exists"
else
    test_failed "Essentials configuration missing"
fi

if [ -f "plugins/WelcomeMessages/config.yml" ]; then
    test_passed "WelcomeMessages configuration exists"
else
    test_failed "WelcomeMessages configuration missing"
fi

# Test 4: Check database initialization script
if [ -f "config/init-db.sql" ]; then
    test_passed "Database initialization script exists"
else
    test_failed "Database initialization script missing"
fi

# Test 5: Validate docker-compose configuration
echo ""
echo "🔧 Testing Docker Compose configuration..."
if docker-compose config &> /dev/null; then
    test_passed "Docker Compose configuration is valid"
else
    test_failed "Docker Compose configuration has errors"
    docker-compose config
    exit 1
fi

# Test 6: Check if containers are running
echo ""
echo "🏃 Testing container status..."
if docker-compose ps | grep -q "Up"; then
    test_passed "Containers are running"
    
    # Check specific services
    if docker-compose ps db | grep -q "Up"; then
        test_passed "Database container is running"
    else
        test_warning "Database container is not running"
    fi
    
    if docker-compose ps mc | grep -q "Up"; then
        test_passed "Minecraft server container is running"
    else
        test_warning "Minecraft server container is not running"
    fi
else
    test_warning "No containers are currently running"
    test_info "Run './start-server.sh' to start the server"
fi

# Test 7: Check database connectivity (if running)
echo ""
echo "🗄️  Testing database connectivity..."
if docker-compose ps db | grep -q "Up"; then
    if docker-compose exec -T db mysqladmin ping -h localhost -u authme -pauthme_pass --silent; then
        test_passed "Database is accessible"
        
        # Test database tables
        if docker-compose exec -T db mysql -u authme -pauthme_pass authme -e "SHOW TABLES;" | grep -q "authme"; then
            test_passed "AuthMe tables exist in database"
        else
            test_warning "AuthMe tables not found in database"
        fi
    else
        test_failed "Cannot connect to database"
    fi
else
    test_info "Database container not running - skipping connectivity test"
fi

# Test 8: Check Minecraft server status (if running)
echo ""
echo "🎮 Testing Minecraft server..."
if docker-compose ps mc | grep -q "Up"; then
    # Check if server has started successfully
    if docker-compose logs mc | grep -q "Done"; then
        test_passed "Minecraft server has started successfully"
    else
        test_warning "Minecraft server may still be starting"
        test_info "Check logs with: docker-compose logs mc"
    fi
    
    # Check if plugins are loaded
    if docker-compose logs mc | grep -q "AuthMe"; then
        test_passed "AuthMe plugin is loaded"
    else
        test_warning "AuthMe plugin may not be loaded"
    fi
    
    if docker-compose logs mc | grep -q "Essentials"; then
        test_passed "Essentials plugin is loaded"
    else
        test_warning "Essentials plugin may not be loaded"
    fi
else
    test_info "Minecraft server container not running - skipping server tests"
fi

# Test 9: Check network connectivity
echo ""
echo "🌐 Testing network connectivity..."
if docker-compose ps | grep -q "Up"; then
    # Test if port 25565 is accessible
    if timeout 5 bash -c "</dev/tcp/localhost/25565" 2>/dev/null; then
        test_passed "Minecraft server port (25565) is accessible"
    else
        test_warning "Minecraft server port (25565) is not accessible"
        test_info "Server may still be starting or there may be a firewall issue"
    fi
    
    # Test RCON port
    if timeout 5 bash -c "</dev/tcp/localhost/25575" 2>/dev/null; then
        test_passed "RCON port (25575) is accessible"
    else
        test_warning "RCON port (25575) is not accessible"
    fi
else
    test_info "Containers not running - skipping network tests"
fi

# Test 10: Configuration validation
echo ""
echo "⚙️  Testing configuration validation..."

# Check AuthMe config syntax
if [ -f "plugins/AuthMe/config.yml" ]; then
    if python3 -c "import yaml; yaml.safe_load(open('plugins/AuthMe/config.yml'))" 2>/dev/null; then
        test_passed "AuthMe configuration syntax is valid"
    else
        test_warning "AuthMe configuration may have syntax errors"
    fi
fi

# Check Essentials config syntax
if [ -f "plugins/Essentials/config.yml" ]; then
    if python3 -c "import yaml; yaml.safe_load(open('plugins/Essentials/config.yml'))" 2>/dev/null; then
        test_passed "Essentials configuration syntax is valid"
    else
        test_warning "Essentials configuration may have syntax errors"
    fi
fi

# Summary
echo ""
echo "📊 Test Summary:"
echo "============================================="

# Count results
total_tests=10
if docker-compose ps | grep -q "Up"; then
    test_info "Server is running - all tests executed"
else
    test_info "Server is not running - some tests skipped"
    test_info "Run './start-server.sh' to start the server and run full tests"
fi

echo ""
echo "🎯 Next Steps:"
echo "============================================="
echo "1. If server is not running: ./start-server.sh"
echo "2. Connect to server: localhost:25565"
echo "3. Test registration: /register password password"
echo "4. Test login: /login password"
echo "5. Check welcome messages appear correctly"

echo ""
echo "📚 Useful Commands:"
echo "============================================="
echo "View logs:        docker-compose logs -f mc"
echo "Stop server:      docker-compose down"
echo "Restart server:   docker-compose restart mc"
echo "Database access:  docker-compose exec db mysql -u authme -pauthme_pass authme"

echo ""
echo "✅ Testing complete!"
