services:
  # Database for user authentication data
  db:
    image: mariadb:10.11
    environment:
      MYSQL_ROOT_PASSWORD: minecraft_root_pass
      MYSQL_DATABASE: authme
      MYSQL_USER: authme
      MYSQL_PASSWORD: authme_pass
    volumes:
      - db_data:/var/lib/mysql
      - ./config/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    restart: unless-stopped
    networks:
      - minecraft_net
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "authme", "-pauthme_pass"]
      timeout: 20s
      retries: 10

  mc:
    image: itzg/minecraft-server
    environment:
      EULA: "true"
      TYPE: "PAPER"
      VERSION: "1.21.4"
      MEMORY: "2G"
      # Enable online mode for premium accounts, but AuthMe will handle cracked accounts
      ONLINE_MODE: "true"
      # Custom MOTD with welcome message
      MOTD: "§6Welcome to Our Server!§r\\n§bLogin or Register to play!"
      # Download AuthMe and EssentialsX plugins
      SPIGET_RESOURCES: "6269,9089"  # AuthMe Reloaded, EssentialsX
      # Additional server properties
      DIFFICULTY: "easy"
      GAMEMODE: "survival"
      MAX_PLAYERS: "50"
      VIEW_DISTANCE: "10"
      SPAWN_PROTECTION: "0"
      # Enable command blocks for advanced features
      ENABLE_COMMAND_BLOCK: "true"
      # RCON for server management
      ENABLE_RCON: "true"
      RCON_PASSWORD: "minecraft_rcon"
    ports:
      - "25565:25565"
      - "25575:25575"  # RCON port
    volumes:
      - data:/data
      - ./plugins:/plugins
      - ./config:/config
    depends_on:
      - db
    networks:
      - minecraft_net
    stdin_open: true
    tty: true
    restart: unless-stopped

networks:
  minecraft_net:
    driver: bridge

volumes:
  data: {}
  db_data: {}