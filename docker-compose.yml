services:
  # Database for user authentication data
  db:
    image: mariadb:10.11
    environment:
      MYSQL_ROOT_PASSWORD: minecraft_root_pass
      MYSQL_DATABASE: authme
      MYSQL_USER: authme
      MYSQL_PASSWORD: authme_pass
    volumes:
      - db_data:/var/lib/mysql
      - ./config/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    restart: unless-stopped
    networks:
      - minecraft_net
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "authme", "-pauthme_pass"]
      timeout: 20s
      retries: 10

  mc:
    image: itzg/minecraft-server
    environment:
      EULA: "true"
      TYPE: "PAPER"
      VERSION: "1.21.4"
      MEMORY: "2G"
      # Disable online mode to allow both premium and cracked accounts (AuthMe will handle authentication)
      ONLINE_MODE: "false"
      # Custom MOTD with welcome message
      MOTD: "§6Welcome to Our Server!§r\\n§bLogin or Register to play!"
      # Download plugins directly from GitHub releases
      PLUGINS: |
        https://github.com/AuthMe/AuthMeReloaded/releases/download/5.6.0/AuthMe-5.6.0.jar
        https://github.com/EssentialsX/Essentials/releases/download/2.21.0/EssentialsX-2.21.0.jar
      # Additional server properties
      DIFFICULTY: "easy"
      GAMEMODE: "survival"
      MAX_PLAYERS: "50"
      VIEW_DISTANCE: "10"
      SPAWN_PROTECTION: "0"
      # Enable command blocks for advanced features
      ENABLE_COMMAND_BLOCK: "true"
      # RCON for server management
      ENABLE_RCON: "true"
      RCON_PASSWORD: "minecraft_rcon"
    ports:
      - "25565:25565"
      - "25575:25575"  # RCON port
    volumes:
      - data:/data
      - ./plugins:/plugins
      - ./config:/config
    depends_on:
      - db
    networks:
      - minecraft_net
    stdin_open: true
    tty: true
    restart: unless-stopped

networks:
  minecraft_net:
    driver: bridge

volumes:
  data: {}
  db_data: {}