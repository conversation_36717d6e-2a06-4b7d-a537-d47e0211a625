-- Database initialization script for AuthMe authentication system
-- This script creates the necessary tables and initial data

-- Create the authme database if it doesn't exist
CREATE DATABASE IF NOT EXISTS authme CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Use the authme database
USE authme;

-- Create the main authme table for user authentication
CREATE TABLE IF NOT EXISTS authme (
    id MEDIUMINT(8) UNSIGNED NOT NULL AUTO_INCREMENT,
    username VARCHAR(255) NOT NULL UNIQUE,
    realname VARCHAR(255) NOT NULL,
    password VARCHAR(255) CHARACTER SET ascii COLLATE ascii_bin NOT NULL,
    ip VARCHAR(40) CHARACTER SET ascii COLLATE ascii_bin DEFAULT NULL,
    lastlogin BIGINT(20) DEFAULT NULL,
    x DOUBLE NOT NULL DEFAULT 0,
    y DOUBLE NOT NULL DEFAULT 0,
    z DOUBLE NOT NULL DEFAULT 0,
    world VARCHAR(255) NOT NULL DEFAULT 'world',
    regdate BIGINT(20) NOT NULL DEFAULT 0,
    regip <PERSON><PERSON>HAR(40) CHARACTER SET ascii COLLATE ascii_bin DEFAULT NULL,
    yaw FLOAT DEFAULT NULL,
    pitch FLOAT DEFAULT NULL,
    email VARCHAR(255) DEFAULT NULL,
    isLogged SMALLINT(6) NOT NULL DEFAULT 0,
    hasSession SMALLINT(6) NOT NULL DEFAULT 0,
    totp VARCHAR(32) DEFAULT NULL,
    uuid VARCHAR(36) DEFAULT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY username (username),
    KEY lastlogin (lastlogin),
    KEY regdate (regdate),
    KEY ip (ip),
    KEY uuid (uuid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create table for player sessions
CREATE TABLE IF NOT EXISTS authme_sessions (
    id INT(11) NOT NULL AUTO_INCREMENT,
    username VARCHAR(255) NOT NULL,
    ip VARCHAR(40) NOT NULL,
    session_start BIGINT(20) NOT NULL,
    session_end BIGINT(20) DEFAULT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    PRIMARY KEY (id),
    KEY username (username),
    KEY ip (ip),
    KEY session_start (session_start),
    FOREIGN KEY (username) REFERENCES authme(username) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create table for login attempts tracking
CREATE TABLE IF NOT EXISTS authme_login_attempts (
    id INT(11) NOT NULL AUTO_INCREMENT,
    username VARCHAR(255),
    ip VARCHAR(40) NOT NULL,
    attempt_time BIGINT(20) NOT NULL,
    success BOOLEAN NOT NULL DEFAULT FALSE,
    PRIMARY KEY (id),
    KEY username (username),
    KEY ip (ip),
    KEY attempt_time (attempt_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create table for email verification
CREATE TABLE IF NOT EXISTS authme_email_verification (
    id INT(11) NOT NULL AUTO_INCREMENT,
    username VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    verification_code VARCHAR(255) NOT NULL,
    created_at BIGINT(20) NOT NULL,
    verified BOOLEAN DEFAULT FALSE,
    PRIMARY KEY (id),
    KEY username (username),
    KEY email (email),
    KEY verification_code (verification_code),
    FOREIGN KEY (username) REFERENCES authme(username) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create table for player preferences and welcome message tracking
CREATE TABLE IF NOT EXISTS player_preferences (
    id INT(11) NOT NULL AUTO_INCREMENT,
    username VARCHAR(255) NOT NULL,
    first_join BIGINT(20) NOT NULL,
    last_join BIGINT(20) DEFAULT NULL,
    join_count INT(11) DEFAULT 1,
    welcome_message_shown BOOLEAN DEFAULT FALSE,
    preferred_language VARCHAR(10) DEFAULT 'en',
    show_welcome_title BOOLEAN DEFAULT TRUE,
    show_welcome_sound BOOLEAN DEFAULT TRUE,
    PRIMARY KEY (id),
    UNIQUE KEY username (username),
    KEY first_join (first_join),
    KEY last_join (last_join),
    FOREIGN KEY (username) REFERENCES authme(username) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create table for server statistics
CREATE TABLE IF NOT EXISTS server_stats (
    id INT(11) NOT NULL AUTO_INCREMENT,
    stat_name VARCHAR(255) NOT NULL,
    stat_value VARCHAR(255) NOT NULL,
    updated_at BIGINT(20) NOT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY stat_name (stat_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert initial server statistics
INSERT INTO server_stats (stat_name, stat_value, updated_at) VALUES
('total_registrations', '0', UNIX_TIMESTAMP() * 1000),
('total_logins', '0', UNIX_TIMESTAMP() * 1000),
('server_start_time', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000)
ON DUPLICATE KEY UPDATE 
    stat_value = VALUES(stat_value),
    updated_at = VALUES(updated_at);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_authme_lastlogin ON authme(lastlogin);
CREATE INDEX IF NOT EXISTS idx_authme_regdate ON authme(regdate);
CREATE INDEX IF NOT EXISTS idx_authme_ip ON authme(ip);
CREATE INDEX IF NOT EXISTS idx_sessions_active ON authme_sessions(is_active);
CREATE INDEX IF NOT EXISTS idx_login_attempts_time ON authme_login_attempts(attempt_time);

-- Create a view for active sessions
CREATE OR REPLACE VIEW active_sessions AS
SELECT 
    s.username,
    s.ip,
    s.session_start,
    a.realname,
    a.lastlogin
FROM authme_sessions s
JOIN authme a ON s.username = a.username
WHERE s.is_active = TRUE;

-- Create a view for user statistics
CREATE OR REPLACE VIEW user_stats AS
SELECT 
    a.username,
    a.realname,
    a.regdate,
    a.lastlogin,
    p.join_count,
    p.first_join,
    CASE 
        WHEN a.lastlogin > (UNIX_TIMESTAMP() * 1000 - 300000) THEN 'Online'
        WHEN a.lastlogin > (UNIX_TIMESTAMP() * 1000 - 86400000) THEN 'Recent'
        ELSE 'Offline'
    END as status
FROM authme a
LEFT JOIN player_preferences p ON a.username = p.username;

-- Grant necessary permissions to the authme user
GRANT SELECT, INSERT, UPDATE, DELETE ON authme.* TO 'authme'@'%';
FLUSH PRIVILEGES;

-- Log the database initialization
INSERT INTO server_stats (stat_name, stat_value, updated_at) VALUES
('database_initialized', 'true', UNIX_TIMESTAMP() * 1000)
ON DUPLICATE KEY UPDATE 
    stat_value = 'true',
    updated_at = UNIX_TIMESTAMP() * 1000;
