# AuthMe Messages Configuration
# Custom messages for login and registration system

# Registration messages
registration:
  disabled: '&cRegistration is currently disabled!'
  name_taken: '&cThis username is already registered!'
  register_request: '&6Welcome to the server! &ePlease register with: &a/register <password> <confirmPassword>'
  command_usage: '&cUsage: /register <password> <confirmPassword>'
  reg_only: '&4Y<PERSON> must register to play on this server! Use: &a/register <password> <confirmPassword>'
  success: '&2Successfully registered! You are now logged in.'
  disabled_world: '&cRegistration is disabled in this world!'

# Login messages  
login:
  command_usage: '&cUsage: /login <password>'
  wrong_password: '&cWrong password!'
  success: '&2Successfully logged in! Welcome back!'
  login_request: '&6Welcome back! &ePlease login with: &a/login <password>'
  timeout_error: '&4Login timeout! You took too long to login.'

# Error messages
error:
  denied_command: '&cYou cannot use this command until you login!'
  denied_chat: '&cYou cannot chat until you login!'
  unregistered_user: '&cThis username is not registered!'
  not_logged_in: '&cYou are not logged in!'
  no_permission: '&cYou do not have permission to perform this action!'
  unexpected_error: '&cAn unexpected error occurred, please contact an administrator!'
  max_registration: '&cYou have exceeded the maximum number of registrations for your IP!'
  logged_in: '&cYou are already logged in!'
  kick_for_vip: '&3A VIP player joined the server when it was full!'
  tempban_max_logins: '&cYou have been temporarily banned for failing to log in too many times.'

# Session messages
session:
  valid_session: '&2Session login! Welcome back!'
  invalid_session: '&cYour session has expired, please login again.'

# Password messages
password:
  change_password_success: '&2Password changed successfully!'
  change_password_error: '&cAn error occurred while changing your password!'
  password_error: '&cPassword error!'
  password_error_nick: '&cYou cannot use your username as your password!'
  password_error_unsafe: '&cThe chosen password is not safe, please choose another one!'
  password_error_chars: '&cYour password contains illegal characters. Allowed chars: REG_EX'
  password_error_length: '&cYour password is too short or too long! Please try with another one!'

# Email messages
email:
  usage_email_add: '&cUsage: /email add <email> <confirmEmail>'
  usage_email_change: '&cUsage: /email change <oldEmail> <newEmail>'
  usage_email_recovery: '&cUsage: /email recovery <email>'
  new_email_invalid: '&cInvalid new email!'
  old_email_invalid: '&cInvalid old email!'
  email_invalid: '&cInvalid email address!'
  email_added: '&2Email address successfully added to your account!'
  email_confirm: '&cPlease confirm your email address!'
  email_changed: '&2Email address changed successfully!'
  email_send: '&2Recovery email sent successfully! Please check your email inbox!'
  email_exists: '&cA recovery email was already sent! You can discard it and send a new one using the command below:'
  email_show: '&2Your current email address is: &f%email'
  incomplete_email_settings: '&4Error: not all required settings are set for sending emails. Please contact an administrator!'
  email_already_used: '&4This email address is already being used!'
  email_send_failure: '&cThe email could not be sent. Please contact an administrator!'
  show_no_email: '&2You currently do not have an email address associated with this account.'

# Recovery messages
recovery:
  forgot_password_hint: '&3Forgot your password? Please use /email recovery <yourEmail>'
  command_usage: '&cUsage: /email recovery <email>'
  email_sent: '&2Recovery email sent successfully! Please check your email inbox!'
  code_sent: '&2A recovery code has been sent to your email!'
  incorrect_recovery_code: '&cThe recovery code is not correct! You have &c%count &cmore tries.'
  recovery_code_correct: '&2Recovery code entered correctly!'
  change_password: '&2Please use the command /email setpassword <newPassword> to change your password immediately.'

# Captcha messages
captcha:
  usage_captcha: '&3To login you have to solve a captcha code, use the command: /captcha %captcha_code'
  wrong_captcha: '&cWrong captcha, please type: /captcha %captcha_code'
  valid_captcha: '&2Captcha code solved correctly!'
  captcha_for_registration: '&3To register you have to solve a captcha first, use the command: /captcha %captcha_code'
  register_captcha_valid: '&2Valid captcha! You may now register with /register <password> <confirmPassword>'

# Verification messages
verification:
  code_required: '&3This command requires an email verification! Check your email and follow the instructions.'
  code_sent: '&2A verification code has been sent to your email address!'
  code_correct: '&2Your email has been verified! You can now use this command.'
  code_incorrect: '&cThe verification code is not correct!'
  code_expired: '&3The verification code has expired! Please request a new one.'

# Time messages
time:
  second: 'second'
  seconds: 'seconds'
  minute: 'minute'  
  minutes: 'minutes'
  hour: 'hour'
  hours: 'hours'
  day: 'day'
  days: 'days'

# Two factor authentication
two_factor:
  code_created: '&2Your secret code is %code. You can scan it from here %url'
  confirmation_required: '&cYou need to confirm your code with /2fa confirm <code>'
  code_required: '&3You need to enter your 2fa code with /2fa code <code>'
  already_enabled: '&cTwo factor authentication is already enabled!'
  enable_error_no_code: '&cYou need to enable 2fa first with /2fa add'
  enable_success: '&2Successfully enabled two factor authentication!'
  disable_success: '&2Successfully disabled two factor authentication!'
  disable_error: '&cTwo factor authentication is not enabled!'

# Misc messages
misc:
  accounts_owned_self: '&2You own %count accounts:'
  accounts_owned_other: '&2The player %name has %count accounts:'
  account_not_activated: '&cYour account is not activated yet, please check your emails!'
  password_changed: '&2Password changed successfully!'
  logout: '&2Logged out successfully!'
  reload: '&2Configuration and database have been reloaded correctly!'
  usage_change_password: '&cUsage: /changepassword <oldPassword> <newPassword>'

# On join messages
on_join_validation: '&cPlease login with "/login <password>" or register with "/register <password> <confirmPassword>"'

# Antibot messages
antibot:
  auto_enabled: '&4[AntiBotService] AntiBot enabled due to the huge number of connections!'
  auto_disabled: '&2[AntiBotService] AntiBot disabled after %m minutes!'
