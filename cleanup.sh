#!/bin/bash

# Quick Cleanup Script for Minecraft Server
# This script removes containers and optionally volumes

echo "🧹 Minecraft Server Cleanup..."
echo "=============================="

# Check if Docker is running
if ! docker info &> /dev/null; then
    echo "❌ Docker is not running. Please start Docker Desktop first."
    exit 1
fi

echo "✅ Docker is running"

# Stop and remove containers
echo ""
echo "🛑 Stopping containers..."
docker-compose down --remove-orphans

echo "✅ Containers stopped and removed"

# Ask about volumes
echo ""
read -p "🗄️  Do you want to remove volumes (this will delete all data)? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🗄️  Removing volumes..."
    docker-compose down -v
    docker volume prune -f
    echo "✅ Volumes removed"
else
    echo "ℹ️  Volumes preserved"
fi

# Ask about images
echo ""
read -p "🖼️  Do you want to remove images (will need to re-download)? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🖼️  Removing images..."
    docker rmi itzg/minecraft-server:latest 2>/dev/null || true
    docker rmi mariadb:10.11 2>/dev/null || true
    docker image prune -f
    echo "✅ Images removed"
else
    echo "ℹ️  Images preserved"
fi

# Clean up networks
echo ""
echo "🌐 Cleaning up networks..."
docker network prune -f
echo "✅ Networks cleaned"

echo ""
echo "🎉 Cleanup complete!"
echo ""
echo "📋 Next steps:"
echo "  To start fresh: ./reset-and-rebuild.sh"
echo "  To start normally: ./start-server.sh"
