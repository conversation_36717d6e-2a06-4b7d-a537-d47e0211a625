<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page - No Particles</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        /* NUCLEAR PARTICLE KILLER */
        .particle,
        div.particle,
        [class*="particle"],
        #particles,
        .particles,
        div#particles,
        div.particles {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            width: 0 !important;
            height: 0 !important;
        }
        
        body {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: white;
            min-height: 100vh;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1>Test Page - No Particles</h1>
                <p>This page should have NO particles at all.</p>
                
                <!-- Test Modal -->
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#testModal">
                    Open Test Modal
                </button>
                
                <!-- Modal -->
                <div class="modal fade" id="testModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Test Modal</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <p>This modal should work perfectly without any particle interference!</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Particle killer
        setInterval(() => {
            const particles = document.querySelectorAll('.particle');
            const container = document.getElementById('particles');
            
            if (particles.length > 0) {
                console.log('Killing particles:', particles.length);
                particles.forEach(p => p.remove());
            }
            
            if (container) {
                console.log('Killing particles container');
                container.remove();
            }
        }, 100);
        
        console.log('Test page loaded - no particles should appear');
    </script>
</body>
</html>
