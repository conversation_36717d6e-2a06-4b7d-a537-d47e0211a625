#!/bin/bash

# Minecraft Server Web Manager - Deployment Script
# This script helps deploy the containerized Minecraft server with web management

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="docker-compose-with-webapp.yml"
ENV_FILE=".env"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_requirements() {
    log_info "Checking requirements..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    log_success "Requirements check passed"
}

setup_environment() {
    log_info "Setting up environment..."
    
    if [ ! -f "$ENV_FILE" ]; then
        log_warning "Environment file not found. Creating from example..."
        cp .env.example "$ENV_FILE"
        log_info "Please edit $ENV_FILE with your configuration"
        log_info "Important: Change RCON_PASSWORD and FLASK_SECRET_KEY!"
    fi
    
    log_success "Environment setup complete"
}

build_images() {
    log_info "Building Docker images..."
    docker-compose -f "$COMPOSE_FILE" build --no-cache
    log_success "Images built successfully"
}

start_services() {
    local profile=""
    if [ "$1" = "nginx" ]; then
        profile="--profile with-nginx"
        log_info "Starting services with Nginx proxy..."
    else
        log_info "Starting basic services..."
    fi
    
    docker-compose -f "$COMPOSE_FILE" $profile up -d
    log_success "Services started successfully"
}

stop_services() {
    log_info "Stopping services..."
    docker-compose -f "$COMPOSE_FILE" down
    log_success "Services stopped"
}

show_status() {
    log_info "Service status:"
    docker-compose -f "$COMPOSE_FILE" ps
    
    echo ""
    log_info "Access points:"
    echo "  🌐 Web Manager: http://localhost:5000"
    echo "  📁 File Browser: http://localhost:25580"
    echo "  🎮 Minecraft Server: localhost:25565"
    
    if docker-compose -f "$COMPOSE_FILE" ps | grep -q nginx; then
        echo "  🔒 Nginx Proxy: https://localhost"
    fi
}

show_logs() {
    local service="$1"
    if [ -z "$service" ]; then
        log_info "Showing all logs..."
        docker-compose -f "$COMPOSE_FILE" logs -f
    else
        log_info "Showing logs for $service..."
        docker-compose -f "$COMPOSE_FILE" logs -f "$service"
    fi
}

backup_data() {
    local backup_name="backup_$(date +%Y%m%d_%H%M%S).tar.gz"
    log_info "Creating backup: $backup_name"
    
    docker-compose -f "$COMPOSE_FILE" exec -T mc tar -czf - /data/world | gzip > "$backup_name"
    log_success "Backup created: $backup_name"
}

update_services() {
    log_info "Updating services..."
    docker-compose -f "$COMPOSE_FILE" pull
    docker-compose -f "$COMPOSE_FILE" up -d
    log_success "Services updated"
}

show_help() {
    echo "Minecraft Server Web Manager - Deployment Script"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  setup           Setup environment and check requirements"
    echo "  build           Build Docker images"
    echo "  start [nginx]   Start services (optionally with nginx)"
    echo "  stop            Stop all services"
    echo "  restart         Restart all services"
    echo "  status          Show service status and access points"
    echo "  logs [service]  Show logs (all or specific service)"
    echo "  backup          Create backup of world data"
    echo "  update          Update and restart services"
    echo "  clean           Remove all containers and volumes"
    echo "  help            Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 setup                 # Initial setup"
    echo "  $0 start                 # Start basic services"
    echo "  $0 start nginx           # Start with nginx proxy"
    echo "  $0 logs webapp           # Show webapp logs"
    echo "  $0 backup                # Create world backup"
}

clean_all() {
    log_warning "This will remove all containers and volumes. Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        log_info "Cleaning up..."
        docker-compose -f "$COMPOSE_FILE" down -v --remove-orphans
        docker system prune -f
        log_success "Cleanup complete"
    else
        log_info "Cleanup cancelled"
    fi
}

# Main script
case "$1" in
    setup)
        check_requirements
        setup_environment
        ;;
    build)
        build_images
        ;;
    start)
        start_services "$2"
        show_status
        ;;
    stop)
        stop_services
        ;;
    restart)
        stop_services
        start_services "$2"
        show_status
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs "$2"
        ;;
    backup)
        backup_data
        ;;
    update)
        update_services
        ;;
    clean)
        clean_all
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        log_error "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
