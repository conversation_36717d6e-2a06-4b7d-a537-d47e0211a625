# Minecraft Server Web Manager - Docker Setup

A complete Docker-based solution for running a Minecraft server with a professional web management interface.

## Features

- 🎮 **Minecraft Server** (Vanilla 1.21.4) with RCON enabled
- 🌐 **Web Management Interface** with real-time monitoring
- 📁 **File Browser** for server file management
- 🔒 **Nginx Reverse Proxy** with SSL support (optional)
- 📊 **Performance Monitoring** with TPS, CPU, and memory metrics
- 👥 **Player Management** (kick, ban, whitelist, teleport)
- 💬 **Server Console** with live log viewing
- 🔄 **Auto-restart** and health checks

## Quick Start

### 1. Clone and Setup

```bash
# Copy environment file
cp .env.example .env

# Edit configuration
nano .env
```

### 2. Basic Deployment (Webapp + Minecraft + File Browser)

```bash
# Build and start services
docker-compose -f docker-compose-with-webapp.yml up -d

# View logs
docker-compose -f docker-compose-with-webapp.yml logs -f
```

### 3. Production Deployment (with Nginx SSL)

```bash
# Create SSL certificates directory
mkdir -p ssl

# Generate self-signed certificates (or use your own)
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout ssl/key.pem -out ssl/cert.pem

# Start with nginx
docker-compose -f docker-compose-with-webapp.yml --profile with-nginx up -d
```

## Access Points

| Service | URL | Description |
|---------|-----|-------------|
| Web Manager | http://localhost:5000 | Main management interface |
| File Browser | http://localhost:25580 | Server file management |
| Minecraft Server | localhost:25565 | Game server connection |
| Nginx (SSL) | https://localhost | Production proxy (if enabled) |

## Configuration

### Environment Variables

Edit `.env` file to customize your setup:

```env
# Minecraft Configuration
MINECRAFT_VERSION=1.21.4
MINECRAFT_MEMORY=4G
MINECRAFT_ONLINE_MODE=FALSE

# Security
RCON_PASSWORD=your-secure-password
FLASK_SECRET_KEY=your-super-secret-key

# Ports
MINECRAFT_PORT=25565
WEBAPP_PORT=5000
FILEBROWSER_PORT=25580
```

### Docker Compose Profiles

- **Default**: Minecraft + Webapp + File Browser
- **with-nginx**: Adds Nginx reverse proxy with SSL

## Management Commands

### Service Management

```bash
# Start all services
docker-compose -f docker-compose-with-webapp.yml up -d

# Stop all services
docker-compose -f docker-compose-with-webapp.yml down

# Restart webapp only
docker-compose -f docker-compose-with-webapp.yml restart webapp

# View logs
docker-compose -f docker-compose-with-webapp.yml logs -f webapp
```

### Minecraft Server Management

```bash
# Execute RCON commands
docker-compose -f docker-compose-with-webapp.yml exec mc rcon-cli "list"

# View server logs
docker-compose -f docker-compose-with-webapp.yml logs -f mc

# Backup world data
docker-compose -f docker-compose-with-webapp.yml exec mc tar -czf /data/backup-$(date +%Y%m%d).tar.gz /data/world
```

### Development

```bash
# Build webapp image
docker-compose -f docker-compose-with-webapp.yml build webapp

# Run in development mode
FLASK_ENV=development docker-compose -f docker-compose-with-webapp.yml up webapp
```

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx Proxy   │    │   Web Manager   │    │ Minecraft Server│
│   (Optional)    │◄──►│    (Flask)      │◄──►│     (Java)      │
│   Port 80/443   │    │    Port 5000    │    │   Port 25565    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │  File Browser   │    │   Docker Stats  │
                       │   Port 25580    │    │   (Monitoring)  │
                       └─────────────────┘    └─────────────────┘
```

## Security Considerations

### Production Deployment

1. **Change Default Passwords**:
   ```env
   RCON_PASSWORD=your-secure-password
   FLASK_SECRET_KEY=your-super-secret-key
   ```

2. **Use SSL Certificates**:
   - Place real certificates in `ssl/` directory
   - Enable nginx profile for HTTPS

3. **Firewall Configuration**:
   ```bash
   # Allow only necessary ports
   ufw allow 25565/tcp  # Minecraft
   ufw allow 443/tcp    # HTTPS
   ufw allow 22/tcp     # SSH
   ```

4. **Regular Updates**:
   ```bash
   # Update images
   docker-compose -f docker-compose-with-webapp.yml pull
   docker-compose -f docker-compose-with-webapp.yml up -d
   ```

## Troubleshooting

### Common Issues

1. **Webapp can't connect to Minecraft**:
   ```bash
   # Check if RCON is enabled
   docker-compose -f docker-compose-with-webapp.yml exec mc rcon-cli "list"
   ```

2. **Permission denied errors**:
   ```bash
   # Fix file permissions
   sudo chown -R 1000:1000 ./data
   ```

3. **Port conflicts**:
   ```bash
   # Check what's using ports
   netstat -tulpn | grep :5000
   ```

### Logs and Debugging

```bash
# View all logs
docker-compose -f docker-compose-with-webapp.yml logs

# Debug webapp
docker-compose -f docker-compose-with-webapp.yml exec webapp python -c "from minecraft_manager import MinecraftServerManager; print(MinecraftServerManager('.').get_server_status())"

# Check container health
docker-compose -f docker-compose-with-webapp.yml ps
```

## Backup and Recovery

### Automated Backups

```bash
# Create backup script
cat > backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
docker-compose -f docker-compose-with-webapp.yml exec -T mc tar -czf - /data/world | gzip > "backup_${DATE}.tar.gz"
EOF

chmod +x backup.sh

# Add to crontab for daily backups
echo "0 2 * * * /path/to/backup.sh" | crontab -
```

### Recovery

```bash
# Restore from backup
docker-compose -f docker-compose-with-webapp.yml down
gunzip -c backup_20241214_020000.tar.gz | docker run --rm -i -v atm8_mc-data:/data alpine tar -xzf - -C /
docker-compose -f docker-compose-with-webapp.yml up -d
```

## Performance Tuning

### Resource Limits

Add to docker-compose.yml:

```yaml
services:
  mc:
    deploy:
      resources:
        limits:
          memory: 6G
          cpus: '2.0'
        reservations:
          memory: 4G
          cpus: '1.0'
```

### JVM Tuning

```yaml
environment:
  JVM_OPTS: "-Xms4G -Xmx4G -XX:+UseG1GC -XX:+ParallelRefProcEnabled"
```

## Support

For issues and questions:
- Check logs: `docker-compose logs`
- Verify configuration: `docker-compose config`
- Test connectivity: `curl http://localhost:5000/api/status`
