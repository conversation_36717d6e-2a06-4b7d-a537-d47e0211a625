#!/bin/bash

# Test script for Docker deployment
set -e

echo "🧪 Testing Docker deployment..."

# Test 1: Build the image
echo "📦 Building webapp image..."
docker build -t minecraft-webapp .

# Test 2: Check if image was created
echo "🔍 Checking image..."
docker images | grep minecraft-webapp

# Test 3: Test basic container run
echo "🚀 Testing container startup..."
docker run --rm -d --name test-webapp -p 5001:5000 minecraft-webapp

# Wait for container to start
sleep 10

# Test 4: Check if webapp is responding
echo "🌐 Testing webapp response..."
if curl -f http://localhost:5001/api/status; then
    echo "✅ Webapp is responding!"
else
    echo "❌ Webapp is not responding"
    docker logs test-webapp
fi

# Cleanup
echo "🧹 Cleaning up test container..."
docker stop test-webapp

echo "✅ Docker test completed!"
