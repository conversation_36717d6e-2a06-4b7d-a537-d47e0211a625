{% extends "base.html" %}

{% block title %}Players - Minecraft Server Manager{% endblock %}

{% block head %}
<style>
    /* NUCLEAR PARTICLE KILLER CSS */
    .particle,
    div.particle,
    [class*="particle"],
    #particles,
    .particles,
    div#particles,
    div.particles {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        width: 0 !important;
        height: 0 !important;
        position: absolute !important;
        left: -99999px !important;
        top: -99999px !important;
        z-index: -99999 !important;
        pointer-events: none !important;
        animation: none !important;
        transform: scale(0) !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="row g-4">
    <div class="col-lg-8">
        <div class="card glass-intense animate__animated animate__fadeInUp">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users me-2"></i>Online Players
                </h5>
                <div class="d-flex align-items-center gap-2">
                    <span class="badge bg-primary fs-6 animate__animated animate__pulse animate__infinite">
                        {{ online_players.online_count }}/{{ online_players.max_players }}
                    </span>
                    <button class="btn btn-sm btn-outline-primary" onclick="refreshPlayers()" data-bs-toggle="tooltip" title="Refresh players">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                {% if detailed_players %}
                <div class="row g-3">
                    {% for player in detailed_players %}
                    <div class="col-md-6">
                        <div class="card player-card glass border-0 shadow-sm animate__animated animate__fadeInUp" style="animation-delay: {{ loop.index0 * 0.1 }}s">
                            <div class="card-body p-4">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="flex-shrink-0">
                                        <div class="bg-success bg-opacity-20 rounded-circle p-2 position-relative">
                                            <i class="fas fa-user text-success"></i>
                                            <div class="position-absolute top-0 start-0 w-100 h-100 rounded-circle" style="background: radial-gradient(circle, rgba(16, 185, 129, 0.3) 0%, transparent 70%); animation: pulse 2s infinite;"></div>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="mb-1 fw-bold text-white">{{ player.name }}</h6>
                                        <span class="badge bg-success">
                                            <i class="fas fa-circle me-1" style="font-size: 0.6rem;"></i>Online
                                        </span>
                                    </div>
                                </div>

                                <div class="row g-2 mb-3">
                                    {% if player.position %}
                                    <div class="col-12">
                                        <div class="d-flex align-items-center text-muted">
                                            <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                                            <small>
                                                <strong>Position:</strong>
                                                {{ "%.1f"|format(player.position.x) }},
                                                {{ "%.1f"|format(player.position.y) }},
                                                {{ "%.1f"|format(player.position.z) }}
                                            </small>
                                        </div>
                                    </div>
                                    {% endif %}

                                    {% if player.health %}
                                    <div class="col-6">
                                        <div class="d-flex align-items-center text-muted">
                                            <i class="fas fa-heart me-2 text-danger"></i>
                                            <small><strong>Health:</strong> {{ player.health }}/20</small>
                                        </div>
                                    </div>
                                    {% endif %}

                                    {% if player.gamemode is defined %}
                                    <div class="col-6">
                                        <div class="d-flex align-items-center text-muted">
                                            <i class="fas fa-gamepad me-2 text-info"></i>
                                            <small><strong>Mode:</strong> {{ {0: "Survival", 1: "Creative", 2: "Adventure", 3: "Spectator"}.get(player.gamemode, "Unknown") }}</small>
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="d-flex gap-1 flex-wrap">
                                    <button type="button" class="btn btn-sm btn-outline-warning" data-bs-toggle="modal" data-bs-target="#kickModal" onclick="setPlayerName('{{ player.name }}')" data-bs-toggle="tooltip" title="Kick player">
                                        <i class="fas fa-sign-out-alt"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#banModal" onclick="setPlayerName('{{ player.name }}')" data-bs-toggle="tooltip" title="Ban player">
                                        <i class="fas fa-ban"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#teleportModal" onclick="setPlayerName('{{ player.name }}')" data-bs-toggle="tooltip" title="Teleport player">
                                        <i class="fas fa-location-arrow"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#opModal" onclick="setPlayerName('{{ player.name }}')" data-bs-toggle="tooltip" title="Give operator status">
                                        <i class="fas fa-crown"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="sendPrivateMessage('{{ player.name }}')" data-bs-toggle="tooltip" title="Send private message">
                                        <i class="fas fa-comment"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-5">
                    <div class="animate__animated animate__fadeIn">
                        <i class="fas fa-user-slash fa-4x text-muted mb-3"></i>
                        <h5 class="text-muted mb-2">No Players Online</h5>
                        <p class="text-muted">Players will appear here when they join the server</p>
                        <button class="btn btn-outline-primary" onclick="refreshPlayers()">
                            <i class="fas fa-sync-alt me-2"></i>Refresh
                        </button>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card glass-intense animate__animated animate__fadeInRight">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Whitelist
                </h5>
                <div class="d-flex align-items-center gap-2">
                    {% if whitelist %}
                    <span class="badge bg-success fs-6">
                        {{ whitelist|length }}
                    </span>
                    {% endif %}
                    <button class="btn btn-sm btn-outline-primary" onclick="refreshWhitelist()" data-bs-toggle="tooltip" title="Refresh whitelist">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#addWhitelistModal" data-bs-toggle="tooltip" title="Add player to whitelist">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
            <div class="card-body" style="max-height: 200px; overflow-y: auto;">
                {% if whitelist %}
                    {% for player in whitelist %}
                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-success bg-opacity-10 rounded animate__animated animate__fadeIn border border-success border-opacity-25" style="animation-delay: {{ loop.index0 * 0.1 }}s">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-user-check text-success me-2"></i>
                            <span class="fw-medium">{{ player }}</span>
                        </div>
                        <button class="btn btn-sm btn-outline-danger" onclick="confirmRemoveWhitelist('{{ player }}')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    {% endfor %}
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-list fa-2x text-muted mb-2"></i>
                    <p class="text-muted mb-0">No whitelisted players</p>
                    <small class="text-muted">Whitelist is currently disabled</small>
                </div>
                {% endif %}
            </div>
        </div>

        <div class="card glass-intense mt-4 animate__animated animate__fadeInRight animate__delay-1s">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-ban me-2"></i>Banned Players
                </h5>
                <div class="d-flex align-items-center gap-2">
                    {% if banlist %}
                    <span class="badge bg-danger fs-6">
                        {{ banlist|length }}
                    </span>
                    {% endif %}
                    <button class="btn btn-sm btn-outline-primary" onclick="refreshBanlist()" data-bs-toggle="tooltip" title="Refresh ban list">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
            <div class="card-body" style="max-height: 250px; overflow-y: auto;">
                {% if banlist %}
                    {% for player in banlist %}
                    <div class="d-flex justify-content-between align-items-center mb-2 p-3 bg-danger bg-opacity-10 rounded animate__animated animate__fadeIn border border-danger border-opacity-25" style="animation-delay: {{ loop.index0 * 0.1 }}s">
                        <div class="d-flex align-items-center flex-grow-1">
                            <div class="flex-shrink-0">
                                <div class="bg-danger bg-opacity-20 rounded-circle p-2">
                                    <i class="fas fa-user-slash text-danger"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1 fw-bold text-danger">{{ player }}</h6>
                                <span class="badge bg-danger-subtle text-danger">
                                    <i class="fas fa-ban me-1" style="font-size: 0.6rem;"></i>Banned
                                </span>
                            </div>
                        </div>
                        <div class="d-flex gap-1">
                            <button type="button" class="btn btn-sm btn-outline-success"
                                    onclick="confirmUnban('{{ player }}')"
                                    title="Unban player">
                                <i class="fas fa-undo"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-info"
                                    onclick="showBanInfo('{{ player }}')"
                                    data-bs-toggle="tooltip"
                                    title="View ban details">
                                <i class="fas fa-info-circle"></i>
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                <div class="text-center py-4">
                    <div class="animate__animated animate__fadeIn">
                        <i class="fas fa-shield-alt fa-4x text-muted mb-3"></i>
                        <h5 class="text-muted mb-2">No Banned Players</h5>
                        <p class="text-muted">Server is clean! No players are currently banned.</p>
                        <button class="btn btn-outline-primary" onclick="refreshBanlist()">
                            <i class="fas fa-sync-alt me-2"></i>Refresh
                        </button>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <div class="card glass-intense mt-4 animate__animated animate__fadeInRight animate__delay-2s">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-3">
                    <button class="btn btn-outline-warning d-flex align-items-center" data-bs-toggle="modal" data-bs-target="#kickModal">
                        <i class="fas fa-sign-out-alt me-2"></i>
                        <div class="text-start">
                            <div class="fw-medium">Kick Player</div>
                            <small class="text-muted">Remove player temporarily</small>
                        </div>
                    </button>
                    <button class="btn btn-outline-danger d-flex align-items-center" data-bs-toggle="modal" data-bs-target="#banModal">
                        <i class="fas fa-ban me-2"></i>
                        <div class="text-start">
                            <div class="fw-medium">Ban Player</div>
                            <small class="text-muted">Permanently ban player</small>
                        </div>
                    </button>
                    <button class="btn btn-outline-success d-flex align-items-center" data-bs-toggle="modal" data-bs-target="#opModal">
                        <i class="fas fa-crown me-2"></i>
                        <div class="text-start">
                            <div class="fw-medium">Give Operator</div>
                            <small class="text-muted">Grant admin privileges</small>
                        </div>
                    </button>
                    <button class="btn btn-outline-info d-flex align-items-center" data-bs-toggle="modal" data-bs-target="#teleportModal">
                        <i class="fas fa-location-arrow me-2"></i>
                        <div class="text-start">
                            <div class="fw-medium">Teleport Player</div>
                            <small class="text-muted">Move player to location</small>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Kick Modal -->
<div class="modal fade" id="kickModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-warning bg-opacity-10 border-0">
                <h5 class="modal-title d-flex align-items-center">
                    <i class="fas fa-sign-out-alt text-warning me-2"></i>
                    Kick Player
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('kick_player') }}" class="modal-form" data-success-message="Player kicked successfully!">
                <div class="modal-body p-4">
                    <div class="alert alert-warning border-0 bg-warning bg-opacity-10">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        This will temporarily remove the player from the server.
                    </div>
                    <div class="mb-3">
                        <label for="kickPlayerName" class="form-label fw-medium">Player Name</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-user"></i>
                            </span>
                            <input type="text" class="form-control" id="kickPlayerName" name="player_name" placeholder="Enter player name" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="kickReason" class="form-label fw-medium">Reason</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-comment"></i>
                            </span>
                            <input type="text" class="form-control" id="kickReason" name="reason" value="Kicked by admin" placeholder="Enter kick reason">
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 p-4">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-sign-out-alt me-2"></i>Kick Player
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Ban Modal -->
<div class="modal fade" id="banModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-danger bg-opacity-10 border-0">
                <h5 class="modal-title d-flex align-items-center">
                    <i class="fas fa-ban text-danger me-2"></i>
                    Ban Player
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('ban_player') }}" class="modal-form" data-success-message="Player banned successfully!">
                <div class="modal-body p-4">
                    <div class="alert alert-danger border-0 bg-danger bg-opacity-10">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> This will permanently ban the player from the server.
                    </div>
                    <div class="mb-3">
                        <label for="banPlayerName" class="form-label fw-medium">Player Name</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-user"></i>
                            </span>
                            <input type="text" class="form-control" id="banPlayerName" name="player_name" placeholder="Enter player name" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="banReason" class="form-label fw-medium">Reason</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-comment"></i>
                            </span>
                            <input type="text" class="form-control" id="banReason" name="reason" value="Banned by admin" placeholder="Enter ban reason">
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 p-4">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-ban me-2"></i>Ban Player
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Teleport Modal -->
<div class="modal fade" id="teleportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Teleport Player</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('teleport_player') }}" class="modal-form" data-success-message="Player teleported successfully!">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="tpPlayerName" class="form-label">Player Name</label>
                        <input type="text" class="form-control" id="tpPlayerName" name="player_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="tpTarget" class="form-label">Target (player name or coordinates)</label>
                        <input type="text" class="form-control" id="tpTarget" name="target" placeholder="PlayerName or 0 64 0" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-info">Teleport</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- OP Modal -->
<div class="modal fade" id="opModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Give Operator Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('op_player') }}" class="modal-form" data-success-message="Player given operator status!">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="opPlayerName" class="form-label">Player Name</label>
                        <input type="text" class="form-control" id="opPlayerName" name="player_name" required>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        Operator status gives players full administrative privileges.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Give OP</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

<!-- Add Whitelist Modal -->
<div class="modal fade" id="addWhitelistModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-success bg-opacity-10 border-0">
                <h5 class="modal-title d-flex align-items-center">
                    <i class="fas fa-user-plus text-success me-2"></i>
                    Add to Whitelist
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('add_whitelist') }}" class="modal-form" data-success-message="Player added to whitelist!">
                <div class="modal-body p-4">
                    <div class="mb-3">
                        <label for="whitelistPlayerName" class="form-label fw-medium">Player Name</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-user"></i>
                            </span>
                            <input type="text" class="form-control" id="whitelistPlayerName" name="player_name" placeholder="Enter player name" required>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 p-4">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-user-plus me-2"></i>Add to Whitelist
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Unban Modal -->
<div class="modal fade" id="unbanModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-success bg-opacity-10 border-0">
                <h5 class="modal-title d-flex align-items-center">
                    <i class="fas fa-undo text-success me-2"></i>
                    Unban Player
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('unban_player') }}" class="modal-form" data-success-message="Player unbanned successfully!">
                <div class="modal-body p-4">
                    <div class="alert alert-success border-0 bg-success bg-opacity-10">
                        <i class="fas fa-info-circle me-2"></i>
                        This will remove the player from the ban list and allow them to rejoin the server.
                    </div>
                    <div class="mb-3">
                        <label for="unbanPlayerName" class="form-label fw-medium">Player Name</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-user"></i>
                            </span>
                            <input type="text" class="form-control" id="unbanPlayerName" name="player_name" placeholder="Enter player name" required readonly>
                        </div>
                        <div class="form-text">
                            <i class="fas fa-lightbulb me-1"></i>
                            The player will be able to join the server immediately after unbanning.
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 p-4">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-undo me-2"></i>Unban Player
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

{% block scripts %}

<script>
function setPlayerName(playerName) {
    document.getElementById('kickPlayerName').value = playerName;
    document.getElementById('banPlayerName').value = playerName;
    document.getElementById('tpPlayerName').value = playerName;
    document.getElementById('opPlayerName').value = playerName;
}

function confirmUnban(playerName) {
    Swal.fire({
        title: 'Unban Player?',
        html: `
            <div class="text-start">
                <p><strong>Player:</strong> ${playerName}</p>
                <p class="text-muted">This will remove the player from the ban list and allow them to rejoin the server.</p>
            </div>
        `,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#22c55e',
        cancelButtonColor: '#6b7280',
        confirmButtonText: '<i class="fas fa-undo me-2"></i>Yes, unban!',
        cancelButtonText: 'Cancel',
        customClass: {
            popup: 'text-start'
        }
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: 'Unbanning Player...',
                text: 'Please wait while we process your request.',
                icon: 'info',
                allowOutsideClick: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Perform the unban
            fetch('/action/unban', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: 'player_name=' + encodeURIComponent(playerName)
            })
            .then(response => {
                console.log('Unban response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Unban response data:', data);
                Swal.fire({
                    title: 'Success!',
                    text: data.message || `${playerName} has been unbanned successfully!`,
                    icon: 'success',
                    confirmButtonColor: '#22c55e'
                }).then(() => {
                    // Refresh the page to show updated ban list
                    window.location.reload();
                });
            })
            .catch(error => {
                console.error('Unban error:', error);
                Swal.fire({
                    title: 'Error!',
                    text: error.message || 'An unexpected error occurred. Please try again.',
                    icon: 'error',
                    confirmButtonColor: '#ef4444'
                });
            });
        }
    });
}



function refreshPlayers() {
    const btn = event.target.closest('button');
    const icon = btn.querySelector('i');
    icon.classList.add('fa-spin');

    // Fetch updated detailed player data in a single call
    fetch('/api/players/detailed')
        .then(response => response.json())
        .then(data => {
            // Update player count badge
            const badge = document.querySelector('.badge');
            if (badge && data.online_count !== undefined) {
                badge.textContent = `${data.online_count}/${data.max_players}`;
            }

            // Update the player list with detailed data
            updatePlayerList(data.detailed_players || []);
            ToastManager.show('Player data refreshed!', 'success');
        })
        .catch(error => {
            console.error('Error refreshing players:', error);
            ToastManager.show('Failed to refresh player data', 'error');
        })
        .finally(() => {
            icon.classList.remove('fa-spin');
        });
}

function updatePlayerList(detailedPlayers) {
    const cardBody = document.querySelector('.card-body');
    if (!cardBody) return;

    if (detailedPlayers.length === 0) {
        // Show "No Players Online" message
        cardBody.innerHTML = `
            <div class="text-center py-5">
                <div class="animate__animated animate__fadeIn">
                    <i class="fas fa-user-slash fa-4x text-muted mb-3"></i>
                    <h5 class="text-muted mb-2">No Players Online</h5>
                    <p class="text-muted">Players will appear here when they join the server</p>
                    <button class="btn btn-outline-primary" onclick="refreshPlayers()">
                        <i class="fas fa-sync-alt me-2"></i>Refresh
                    </button>
                </div>
            </div>
        `;
    } else {
        // Create player cards
        const playersHtml = detailedPlayers.map((player, index) => {
            const positionHtml = player.position ? `
                <div class="col-12">
                    <div class="d-flex align-items-center text-muted">
                        <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                        <small>
                            <strong>Position:</strong>
                            ${player.position.x.toFixed(1)},
                            ${player.position.y.toFixed(1)},
                            ${player.position.z.toFixed(1)}
                        </small>
                    </div>
                </div>
            ` : '';

            const healthHtml = player.health !== undefined ? `
                <div class="col-6">
                    <div class="d-flex align-items-center text-muted">
                        <i class="fas fa-heart me-2 text-danger"></i>
                        <small><strong>Health:</strong> ${player.health}/20</small>
                    </div>
                </div>
            ` : '';

            const gamemodeHtml = player.gamemode !== undefined ? `
                <div class="col-6">
                    <div class="d-flex align-items-center text-muted">
                        <i class="fas fa-gamepad me-2 text-info"></i>
                        <small><strong>Mode:</strong> ${
                            {0: "Survival", 1: "Creative", 2: "Adventure", 3: "Spectator"}[player.gamemode] || "Unknown"
                        }</small>
                    </div>
                </div>
            ` : '';

            return `
                <div class="col-md-6">
                    <div class="card player-card border-0 shadow-sm animate__animated animate__fadeInUp" style="animation-delay: ${index * 0.1}s;">
                        <div class="card-body p-4">
                            <div class="d-flex align-items-center mb-3">
                                <div class="flex-shrink-0">
                                    <div class="bg-success bg-opacity-10 rounded-circle p-2">
                                        <i class="fas fa-user text-success"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-1 fw-bold">${player.name}</h6>
                                    <span class="badge bg-success-subtle text-success">
                                        <i class="fas fa-circle me-1" style="font-size: 0.6rem;"></i>Online
                                    </span>
                                </div>
                            </div>

                            <div class="row g-2 mb-3">
                                ${positionHtml}
                                ${healthHtml}
                                ${gamemodeHtml}
                            </div>

                            <div class="d-flex gap-1 flex-wrap">
                                <button type="button" class="btn btn-sm btn-outline-warning" data-bs-toggle="modal" data-bs-target="#kickModal" onclick="setPlayerName('${player.name}')" data-bs-toggle="tooltip" title="Kick player">
                                    <i class="fas fa-sign-out-alt"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#banModal" onclick="setPlayerName('${player.name}')" data-bs-toggle="tooltip" title="Ban player">
                                    <i class="fas fa-ban"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#teleportModal" onclick="setPlayerName('${player.name}')" data-bs-toggle="tooltip" title="Teleport player">
                                    <i class="fas fa-location-arrow"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#opModal" onclick="setPlayerName('${player.name}')" data-bs-toggle="tooltip" title="Give operator status">
                                    <i class="fas fa-crown"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="sendPrivateMessage('${player.name}')" data-bs-toggle="tooltip" title="Send private message">
                                    <i class="fas fa-comment"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        cardBody.innerHTML = `<div class="row g-3">${playersHtml}</div>`;

        // Re-initialize tooltips for the new elements
        const tooltipTriggerList = [].slice.call(cardBody.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
}

function refreshBanlist() {
    const btn = event.target.closest('button');
    const icon = btn.querySelector('i');
    icon.classList.add('fa-spin');

    fetch('/api/banlist')
        .then(response => response.json())
        .then(data => {
            ToastManager.show('Ban list refreshed!', 'success');
            // Refresh the page to show updated ban list
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        })
        .catch(error => {
            ToastManager.show('Failed to refresh ban list', 'error');
        })
        .finally(() => {
            icon.classList.remove('fa-spin');
        });
}

function refreshWhitelist() {
    const btn = event.target.closest('button');
    const icon = btn.querySelector('i');
    icon.classList.add('fa-spin');

    fetch('/api/whitelist')
        .then(response => response.json())
        .then(data => {
            ToastManager.show('Whitelist refreshed!', 'success');
            // Refresh the page to show updated whitelist
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        })
        .catch(error => {
            ToastManager.show('Failed to refresh whitelist', 'error');
        })
        .finally(() => {
            icon.classList.remove('fa-spin');
        });
}

function confirmRemoveWhitelist(playerName) {
    Swal.fire({
        title: 'Remove from Whitelist?',
        text: `Are you sure you want to remove ${playerName} from the whitelist?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#ef4444',
        cancelButtonColor: '#6b7280',
        confirmButtonText: 'Yes, remove!',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            // Create a form and submit it
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/action/whitelist/remove';

            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'player_name';
            input.value = playerName;

            form.appendChild(input);
            document.body.appendChild(form);
            form.submit();
        }
    });
}

function showBanInfo(playerName) {
    Swal.fire({
        title: `Ban Information`,
        html: `
            <div class="text-start">
                <p><strong>Player:</strong> ${playerName}</p>
                <p><strong>Status:</strong> <span class="badge bg-danger">Banned</span></p>
                <p><strong>Action:</strong> Use the unban button to remove this player from the ban list</p>
            </div>
        `,
        icon: 'info',
        confirmButtonText: 'Close',
        confirmButtonColor: '#6366f1',
        customClass: {
            popup: 'text-start'
        }
    });
}

function sendPrivateMessage(playerName) {
    Swal.fire({
        title: `Send Message to ${playerName}`,
        input: 'text',
        inputPlaceholder: 'Enter your message...',
        showCancelButton: true,
        confirmButtonText: 'Send',
        confirmButtonColor: '#6366f1',
        cancelButtonColor: '#6b7280',
        inputValidator: (value) => {
            if (!value) {
                return 'Please enter a message!';
            }
        }
    }).then((result) => {
        if (result.isConfirmed) {
            // Here you would send the message via API
            ToastManager.show(`Message sent to ${playerName}!`, 'success');
        }
    });
}

// Legacy function - kept for backward compatibility
function confirmAction(message, action, playerName) {
    console.log('Legacy confirmAction called - consider updating to specific functions');
    Swal.fire({
        title: 'Are you sure?',
        text: message,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#ef4444',
        cancelButtonColor: '#6b7280',
        confirmButtonText: 'Yes, proceed!',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            ToastManager.show('Action completed!', 'success');
        }
    });
}

// Emergency particle cleanup function
window.killAllParticles = function() {
    console.log('💀 KILLING ALL PARTICLES');

    // Remove particles container
    const particlesContainer = document.getElementById('particles');
    if (particlesContainer) {
        particlesContainer.innerHTML = '';
        particlesContainer.style.display = 'none';
        console.log('Particles container cleared and hidden');
    }

    // Remove any stray particle elements
    const allParticles = document.querySelectorAll('.particle');
    allParticles.forEach((particle, index) => {
        particle.remove();
        console.log(`Removed particle ${index}`);
    });

    // Stop particle system if it exists
    if (window.particleSystem) {
        window.particleSystem.stop();
        window.particleSystem = null;
        console.log('Particle system stopped and nullified');
    }

    console.log(`💀 Killed ${allParticles.length} particles total`);
    return allParticles.length;
};

// Emergency modal cleanup function - available globally with extensive logging
window.emergencyModalCleanup = function() {
    console.log('🚨 EMERGENCY MODAL CLEANUP TRIGGERED');
    console.log('📊 Current state analysis:');

    // Log current state
    const backdrops = document.querySelectorAll('.modal-backdrop');
    const modals = document.querySelectorAll('.modal');
    const openModals = document.querySelectorAll('.modal.show');
    const bodyClasses = document.body.className;
    const bodyStyle = document.body.style.cssText;

    console.log(`- Found ${backdrops.length} modal backdrops`);
    console.log(`- Found ${modals.length} total modals`);
    console.log(`- Found ${openModals.length} open modals`);
    console.log(`- Body classes: ${bodyClasses}`);
    console.log(`- Body style: ${bodyStyle}`);

    // Log each backdrop
    backdrops.forEach((backdrop, index) => {
        console.log(`- Backdrop ${index}:`, backdrop.className, backdrop.style.cssText);
    });

    // Log each open modal
    openModals.forEach((modal, index) => {
        console.log(`- Open modal ${index}:`, modal.id, modal.className);
    });

    console.log('🧹 Starting cleanup...');

    // Remove ALL modal backdrops
    backdrops.forEach((backdrop, index) => {
        console.log(`Removing backdrop ${index}`);
        backdrop.remove();
    });

    // Hide ALL modals
    modals.forEach((modal, index) => {
        console.log(`Hiding modal ${index}: ${modal.id}`);
        modal.classList.remove('show');
        modal.style.display = 'none';
        modal.setAttribute('aria-hidden', 'true');
        modal.removeAttribute('aria-modal');
        modal.removeAttribute('role');
    });

    // Reset body completely
    console.log('Resetting body state...');
    document.body.classList.remove('modal-open');
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';
    document.body.style.marginRight = '';

    // Remove any Bootstrap modal classes that might be stuck
    document.body.classList.remove('modal-backdrop');

    // Force remove any elements with modal-backdrop class that might be hidden
    const allBackdrops = document.querySelectorAll('[class*="backdrop"], [class*="modal-backdrop"]');
    allBackdrops.forEach((el, index) => {
        console.log(`Force removing backdrop element ${index}:`, el.className);
        el.remove();
    });

    // Check for any elements with high z-index that might be blocking
    const highZElements = Array.from(document.querySelectorAll('*')).filter(el => {
        const zIndex = window.getComputedStyle(el).zIndex;
        return zIndex && parseInt(zIndex) > 1000;
    });

    console.log(`Found ${highZElements.length} elements with high z-index:`);
    highZElements.forEach((el, index) => {
        const zIndex = window.getComputedStyle(el).zIndex;
        console.log(`- Element ${index}: ${el.tagName}.${el.className} z-index: ${zIndex}`);
    });

    // Resume particle system
    if (window.particleSystem) {
        console.log('Resuming particle system...');
        window.particleSystem.resume();
    }

    console.log('✅ Emergency cleanup completed!');

    // Show success message
    if (typeof ToastManager !== 'undefined') {
        ToastManager.show('Emergency cleanup completed!', 'success');
    }

    alert('Emergency modal cleanup completed! Check console for details.');

    // Return diagnostic info
    return {
        backdropsRemoved: backdrops.length,
        modalsHidden: modals.length,
        highZElements: highZElements.length
    };
};

// Alternative modal handler - completely manual control
window.forceCloseAllModals = function() {
    console.log('🚨 FORCE CLOSING ALL MODALS');

    // Destroy all Bootstrap modal instances
    document.querySelectorAll('.modal').forEach(modal => {
        const instance = bootstrap.Modal.getInstance(modal);
        if (instance) {
            try {
                instance.dispose();
                console.log(`Disposed modal instance: ${modal.id}`);
            } catch (e) {
                console.log(`Error disposing modal ${modal.id}:`, e);
            }
        }
    });

    // Manually hide all modals
    document.querySelectorAll('.modal').forEach(modal => {
        modal.classList.remove('show');
        modal.style.display = 'none';
        modal.setAttribute('aria-hidden', 'true');
        modal.removeAttribute('aria-modal');
    });

    // Remove all backdrops
    document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
        backdrop.remove();
    });

    // Reset body
    document.body.classList.remove('modal-open');
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';

    // Resume particle system
    if (window.particleSystem) {
        console.log('Resuming particle system...');
        window.particleSystem.resume();
    }

    console.log('✅ All modals force closed');
};

// IMMEDIATE PARTICLE ANNIHILATOR - RUNS BEFORE ANYTHING ELSE
(function() {
    console.log('🔥 IMMEDIATE PARTICLE ANNIHILATOR STARTING');

    // Override createElement to prevent particle creation
    const originalCreateElement = document.createElement;
    document.createElement = function(tagName) {
        const element = originalCreateElement.call(this, tagName);
        if (tagName.toLowerCase() === 'div') {
            // Check if it's trying to create a particle
            const originalSetAttribute = element.setAttribute;
            element.setAttribute = function(name, value) {
                if (name === 'class' && value && value.includes('particle')) {
                    console.log('🚫 BLOCKED particle creation');
                    return; // Don't set the class
                }
                return originalSetAttribute.call(this, name, value);
            };
        }
        return element;
    };

    // Override appendChild to prevent particle insertion
    const originalAppendChild = Element.prototype.appendChild;
    Element.prototype.appendChild = function(child) {
        if (child && child.classList && child.classList.contains('particle')) {
            console.log('🚫 BLOCKED particle appendChild');
            return child; // Don't actually append
        }
        if (child && child.id === 'particles') {
            console.log('🚫 BLOCKED particles container appendChild');
            return child; // Don't actually append
        }
        return originalAppendChild.call(this, child);
    };

    // Kill particles every 100ms aggressively
    setInterval(function() {
        const particles = document.querySelectorAll('.particle');
        const container = document.getElementById('particles');

        if (particles.length > 0) {
            console.log(`🔥 DESTROYING ${particles.length} particles`);
            particles.forEach(p => {
                try {
                    p.remove();
                } catch (e) {
                    try {
                        p.parentNode.removeChild(p);
                    } catch (e2) {
                        // Ignore
                    }
                }
            });
        }

        if (container) {
            console.log('🔥 DESTROYING particles container');
            try {
                container.remove();
            } catch (e) {
                try {
                    container.parentNode.removeChild(container);
                } catch (e2) {
                    // Ignore
                }
            }
        }
    }, 100);

    console.log('🔥 PARTICLE ANNIHILATOR ACTIVE');
})();

// Handle modal forms with AJAX - Non-blocking modals
document.addEventListener('DOMContentLoaded', function() {

    // Initialize modals manually to avoid backdrop issues
    document.querySelectorAll('.modal').forEach(modalElement => {
        try {
            // Initialize modal with explicit configuration
            const modal = new bootstrap.Modal(modalElement, {
                backdrop: true,
                keyboard: true,
                focus: true
            });
            console.log('Modal initialized successfully:', modalElement.id);

            // Add event listeners for proper cleanup
            modalElement.addEventListener('hidden.bs.modal', function() {
                // Ensure cleanup when modal is hidden
                setTimeout(() => {
                    cleanupModalBackdrops();
                }, 100);
            });

        } catch (error) {
            console.error('Error initializing modal:', modalElement.id, error);
        }
    });

    // Handle unban modal show event
    const unbanModal = document.getElementById('unbanModal');
    if (unbanModal) {
        unbanModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget; // Button that triggered the modal
            const playerName = button.getAttribute('data-player-name');
            console.log('Modal show event - setting player name:', playerName);

            const playerNameInput = document.getElementById('unbanPlayerName');
            if (playerNameInput && playerName) {
                playerNameInput.value = playerName;
                console.log('Successfully set player name in modal:', playerName);
            } else {
                console.error('Failed to set player name - input:', playerNameInput, 'name:', playerName);
            }
        });
    }

    // Function to properly close modal and cleanup backdrop
    function closeModal(modal) {
        console.log('🔒 closeModal called for:', modal ? modal.id : 'unknown modal');

        const modalInstance = bootstrap.Modal.getInstance(modal);
        console.log('📋 Modal instance:', modalInstance);

        if (modalInstance) {
            console.log('🚪 Hiding modal instance...');
            modalInstance.hide();
        } else {
            console.log('⚠️ No modal instance found, manually hiding modal...');
            if (modal) {
                modal.classList.remove('show');
                modal.style.display = 'none';
                modal.setAttribute('aria-hidden', 'true');
            }
        }

        // Force cleanup any lingering backdrops after a short delay
        setTimeout(() => {
            console.log('🧹 Running cleanup after modal close...');
            cleanupModalBackdrops();
        }, 300);
    }

    // Local cleanup function that calls the global one or provides fallback
    function cleanupModalBackdrops() {
        try {
            if (typeof window.cleanupModalBackdrops === 'function') {
                window.cleanupModalBackdrops();
            } else {
                // Fallback if global function not available
                forceCleanupBackdrops();
            }
        } catch (error) {
            console.error('Error in cleanupModalBackdrops:', error);
            forceCleanupBackdrops();
        }
    }

    // Force cleanup function as fallback
    function forceCleanupBackdrops() {
        console.log('Force cleaning modal backdrops...');

        // Remove any lingering modal backdrops
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => {
            backdrop.remove();
        });

        // Remove modal-open class from body
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';

        // Hide any open modals
        const openModals = document.querySelectorAll('.modal.show');
        openModals.forEach(modal => {
            modal.classList.remove('show');
            modal.style.display = 'none';
            modal.setAttribute('aria-hidden', 'true');
        });

        console.log(`Cleaned up ${backdrops.length} backdrops and ${openModals.length} modals`);
    }

    // Handle all modal forms
    document.querySelectorAll('.modal-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault(); // Prevent default form submission

            console.log('🚀 Modal form submission started');

            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            const successMessage = this.getAttribute('data-success-message');
            const modal = this.closest('.modal');

            console.log('📝 Form details:', {
                action: this.action,
                modalId: modal ? modal.id : 'unknown',
                successMessage: successMessage
            });

            // Log current modal state before submission
            const backdrops = document.querySelectorAll('.modal-backdrop');
            console.log(`📊 Before submission - Backdrops: ${backdrops.length}, Body classes: ${document.body.className}`);

            // Show loading state
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
            submitBtn.disabled = true;

            // Create FormData from the form and convert to URL-encoded string
            const formData = new FormData(this);
            const urlEncodedData = new URLSearchParams();

            // Debug: Log form data
            console.log('Form action:', this.action);
            console.log('Form data:');
            for (let [key, value] of formData.entries()) {
                console.log(`${key}: ${value}`);
                urlEncodedData.append(key, value);
            }
            console.log('URL encoded data:', urlEncodedData.toString());

            // Submit via AJAX
            fetch(this.action, {
                method: 'POST',
                body: urlEncodedData,
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers.get('content-type'));

                if (response.ok) {
                    // Try to parse JSON response
                    return response.json().catch(err => {
                        console.error('Failed to parse JSON:', err);
                        // If not JSON, assume success (redirect response)
                        return { success: true };
                    });
                } else {
                    // Handle error responses
                    return response.text().then(text => {
                        console.error('Error response text:', text);
                        try {
                            const errorData = JSON.parse(text);
                            throw new Error(errorData.error || `Server error: ${response.status}`);
                        } catch (parseError) {
                            throw new Error(`Server error: ${response.status} ${response.statusText}`);
                        }
                    });
                }
            })
            .then(data => {
                console.log('✅ Form submission successful:', data);

                // Log modal state before closing
                const backdropsBeforeClose = document.querySelectorAll('.modal-backdrop');
                console.log(`📊 Before closing modal - Backdrops: ${backdropsBeforeClose.length}`);

                // Close modal properly
                console.log('🔒 Closing modal...');
                closeModal(modal);

                // Log modal state after closing
                setTimeout(() => {
                    const backdropsAfterClose = document.querySelectorAll('.modal-backdrop');
                    console.log(`📊 After closing modal - Backdrops: ${backdropsAfterClose.length}, Body classes: ${document.body.className}`);
                }, 500);

                // Show success message
                if (data.message) {
                    ToastManager.show(data.message, 'success');
                } else if (successMessage) {
                    ToastManager.show(successMessage, 'success');
                }

                // Refresh the page after a short delay
                setTimeout(() => {
                    console.log('🔄 Reloading page...');
                    window.location.reload();
                }, 1000);
            })
            .catch(error => {
                console.error('❌ Form submission error:', error);
                console.error('Error stack:', error.stack);
                const errorMessage = error.message || 'An unexpected error occurred. Please try again.';
                console.error('Showing error message:', errorMessage);

                // Log modal state before error cleanup
                const backdropsBeforeError = document.querySelectorAll('.modal-backdrop');
                console.log(`📊 Before error cleanup - Backdrops: ${backdropsBeforeError.length}`);

                // Close modal and cleanup backdrop on error
                console.log('🔒 Closing modal due to error...');
                closeModal(modal);

                // Force emergency cleanup on error
                setTimeout(() => {
                    const backdropsAfterError = document.querySelectorAll('.modal-backdrop');
                    console.log(`📊 After error cleanup - Backdrops: ${backdropsAfterError.length}`);

                    if (backdropsAfterError.length > 0) {
                        console.log('⚠️ Backdrops still present, forcing emergency cleanup...');
                        window.emergencyModalCleanup();
                    }
                }, 500);

                // Fallback error display
                try {
                    ToastManager.show(errorMessage, 'error');
                } catch (toastError) {
                    console.error('ToastManager error:', toastError);
                    alert(errorMessage); // Fallback to alert
                }

                // Restore button state on error
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
    });

    // Add escape key functionality to close modals
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                closeModal(openModal);
            }
        }
    });

    // Add click handler for backdrop cleanup and emergency close
    document.addEventListener('click', function(e) {
        console.log('🖱️ Click detected on:', e.target.tagName, e.target.className);

        // If user clicks on a modal backdrop, force close everything
        if (e.target.classList.contains('modal-backdrop')) {
            console.log('🎯 Backdrop clicked - forcing modal close');
            e.preventDefault();
            e.stopPropagation();
            window.forceCloseAllModals();
            return false;
        }

        // If user clicks outside modal content while modal is open, close it
        const openModal = document.querySelector('.modal.show');
        if (openModal && !openModal.contains(e.target) && !e.target.closest('.modal')) {
            console.log('🎯 Click outside modal detected - closing modal');
            window.forceCloseAllModals();
        }
    });

    // Periodic cleanup of any lingering modal backdrops (safety net)
    setInterval(() => {
        const backdrops = document.querySelectorAll('.modal-backdrop');
        const openModals = document.querySelectorAll('.modal.show');

        // If there are backdrops but no open modals, clean them up
        if (backdrops.length > 0 && openModals.length === 0) {
            console.log('Cleaning up lingering modal backdrops');
            cleanupModalBackdrops();
        }
    }, 5000); // Check every 5 seconds
});

// Auto-refresh player data every 30 seconds
setInterval(() => {
    if (window.location.pathname === '/players') {
        fetch('/api/players/detailed')
            .then(response => response.json())
            .then(data => {
                const badge = document.querySelector('.badge');
                if (badge && data.online_count !== undefined) {
                    const currentCount = parseInt(badge.textContent.split('/')[0]);
                    if (currentCount !== data.online_count) {
                        badge.textContent = `${data.online_count}/${data.max_players}`;
                        badge.classList.add('animate__animated', 'animate__pulse');
                        setTimeout(() => {
                            badge.classList.remove('animate__animated', 'animate__pulse');
                        }, 1000);

                        // Update the full player list with detailed data
                        updatePlayerList(data.detailed_players || []);
                    }
                }
            })
            .catch(error => console.log('Auto-refresh error:', error));
    }
}, 30000);
</script>
{% endblock %}
