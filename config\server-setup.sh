#!/bin/bash

# Minecraft Server Setup Script for Authentication and Welcome Messages
# This script sets up the server with proper configurations

echo "Setting up Minecraft server with authentication and welcome messages..."

# Create necessary directories
mkdir -p /data/plugins/AuthMe
mkdir -p /data/plugins/Essentials
mkdir -p /data/plugins/WelcomeMessages

# Wait for database to be ready
echo "Waiting for database to be ready..."
until nc -z db 3306; do
  echo "Database not ready yet, waiting..."
  sleep 2
done
echo "Database is ready!"

# Create AuthMe database tables if they don't exist
mysql -h db -u authme -pauthme_pass authme << 'EOF'
CREATE TABLE IF NOT EXISTS authme (
  id mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  username varchar(255) NOT NULL UNIQUE,
  realname varchar(255) NOT NULL,
  password varchar(255) CHARACTER SET ascii COLLATE ascii_bin NOT NULL,
  ip varchar(40) CHARACTER SET ascii COLLATE ascii_bin DEFAULT NULL,
  lastlogin bigint(20) DEFAULT NULL,
  x double NOT NULL DEFAULT 0,
  y double NOT NULL DEFAULT 0,
  z double NOT NULL DEFAULT 0,
  world varchar(255) NOT NULL DEFAULT 'world',
  regdate bigint(20) NOT NULL DEFAULT 0,
  regip varchar(40) CHARACTER SET ascii COLLATE ascii_bin DEFAULT NULL,
  yaw float DEFAULT NULL,
  pitch float DEFAULT NULL,
  email varchar(255) DEFAULT NULL,
  isLogged smallint(6) NOT NULL DEFAULT 0,
  hasSession smallint(6) NOT NULL DEFAULT 0,
  totp varchar(32) DEFAULT NULL,
  uuid varchar(36) DEFAULT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY username (username)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
EOF

echo "Database setup complete!"

# Set proper permissions for plugin directories
chmod -R 755 /data/plugins/

echo "Server setup complete! Starting Minecraft server..."
EOF
