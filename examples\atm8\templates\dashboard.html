{% extends "base.html" %}

{% block title %}Dashboard - Minecraft Server Manager{% endblock %}

{% block content %}
<div class="row g-4">
    <div class="col-lg-8">
        <div class="card glass-intense animate__animated animate__fadeInUp">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-server me-2"></i>Server Status
                    <span class="badge bg-success ms-2 animate__animated animate__pulse animate__infinite" style="font-size: 0.7rem;">LIVE</span>
                </h5>
            </div>
            <div class="card-body">
                {% if status %}
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-4 glass p-3 rounded-3 animate__animated animate__fadeInLeft">
                            <div class="flex-shrink-0">
                                <div class="bg-success bg-opacity-20 rounded-circle p-3 position-relative">
                                    <i class="fas fa-circle status-{{ status.status }} fs-4"></i>
                                    <div class="position-absolute top-0 start-0 w-100 h-100 rounded-circle" style="background: radial-gradient(circle, rgba(16, 185, 129, 0.2) 0%, transparent 70%); animation: pulse 2s infinite;"></div>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1 text-white">Server Status</h6>
                                <p id="serverStatus" class="mb-0 text-white-50">{{ status.status.title() }}</p>
                            </div>
                        </div>

                        <div class="d-flex align-items-center mb-4 glass p-3 rounded-3 animate__animated animate__fadeInLeft animate__delay-1s">
                            <div class="flex-shrink-0">
                                <div class="bg-primary bg-opacity-20 rounded-circle p-3 position-relative">
                                    <i class="fas fa-users text-primary fs-4"></i>
                                    <div class="position-absolute top-0 start-0 w-100 h-100 rounded-circle" style="background: radial-gradient(circle, rgba(99, 102, 241, 0.2) 0%, transparent 70%); animation: pulse 2s infinite 0.5s;"></div>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1 text-white">Players Online</h6>
                                <p class="mb-0">
                                    <span class="fs-4 fw-bold text-primary" id="online-count">{{ status.players.online_count }}</span>
                                    <span class="text-white-50">/ <span id="max-players">{{ status.players.max_players }}</span></span>
                                </p>
                            </div>
                        </div>

                        {% if status.players.players %}
                        <div class="mb-3 glass p-3 rounded-3 animate__animated animate__fadeInUp animate__delay-2s">
                            <h6 class="mb-2 text-white">Currently Online</h6>
                            <div class="d-flex flex-wrap gap-2">
                                {% for player in status.players.players %}
                                <span class="badge bg-success border border-success animate__animated animate__fadeIn" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
                                    <i class="fas fa-user me-1"></i>{{ player }}
                                </span>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-4">
                            <div class="flex-shrink-0">
                                <div class="bg-info bg-opacity-10 rounded-circle p-3">
                                    <i class="fas fa-clock text-info fs-4"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">Last Updated</h6>
                                <p class="mb-0 text-muted last-refresh">{{ status.timestamp.split('T')[1].split('.')[0] }}</p>
                            </div>
                        </div>

                        <div class="d-flex align-items-center mb-4">
                            <div class="flex-shrink-0">
                                <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                                    <i class="fas fa-tachometer-alt text-warning fs-4"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">Server Performance
                                    <i class="fas fa-info-circle text-muted ms-1" data-bs-toggle="tooltip"
                                       title="Shows TPS (Ticks Per Second), CPU usage, and memory usage. TPS should be close to 20.0 for optimal performance."
                                       style="font-size: 0.8rem;"></i>
                                </h6>
                                <p id="performanceStatus" class="mb-0 text-muted">
                                    {% if status.tps and 'not available' not in status.tps %}
                                        {{ status.tps }}
                                    {% else %}
                                        Monitoring...
                                    {% endif %}
                                </p>
                            </div>
                        </div>

                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-secondary bg-opacity-10 rounded-circle p-3">
                                    <i class="fas fa-memory text-secondary fs-4"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">Memory</h6>
                                <p id="memoryStatus" class="mb-0 text-muted">
                                    {% if status.memory_usage %}
                                        {{ status.memory_usage }} ({{ status.memory_percent }})
                                    {% else %}
                                        4GB Allocated
                                    {% endif %}
                                </p>
                            </div>
                        </div>

                        <div class="d-flex align-items-center mt-3">
                            <div class="flex-shrink-0">
                                <div class="bg-info bg-opacity-10 rounded-circle p-3">
                                    <i class="fas fa-microchip text-info fs-4"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">CPU Usage</h6>
                                <p id="cpuStatus" class="mb-0 text-muted">
                                    {% if status.cpu_usage %}
                                        {{ status.cpu_usage }}
                                    {% else %}
                                        Monitoring...
                                    {% endif %}
                                </p>
                            </div>
                        </div>

                        <div class="d-flex align-items-center mt-3">
                            <div class="flex-shrink-0">
                                <div class="bg-success bg-opacity-10 rounded-circle p-3">
                                    <i class="fas fa-clock text-success fs-4"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">Uptime</h6>
                                <p id="uptimeStatus" class="mb-0 text-muted">
                                    {% if status.uptime %}
                                        {{ status.uptime }}
                                    {% else %}
                                        Calculating...
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="alert alert-warning border-0 bg-warning bg-opacity-10 animate__animated animate__shakeX">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle text-warning fs-4 me-3"></i>
                        <div>
                            <h6 class="alert-heading mb-1">Connection Issue</h6>
                            <p class="mb-0">Unable to connect to server. Please check if the server is running.</p>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <div class="card glass-intense mt-4 animate__animated animate__fadeInUp animate__delay-1s">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bullhorn me-2"></i>Broadcast Message
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('send_message') }}" onsubmit="handleFormSubmit(this, 'Message sent successfully!')">
                    <div class="mb-3">
                        <label for="messageInput" class="form-label">Message</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-comment"></i>
                            </span>
                            <input type="text" class="form-control" id="messageInput" name="message" placeholder="Enter your message to all players..." required>
                        </div>
                    </div>
                    <div class="d-grid">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-paper-plane me-2"></i>Send to All Players
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card glass-intense animate__animated animate__fadeInRight">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>Recent Activity
                    <button class="btn btn-sm btn-outline-primary ms-auto" onclick="refreshActivity()" data-bs-toggle="tooltip" title="Refresh activity">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </h5>
            </div>
            <div class="card-body" style="max-height: 300px; overflow-y: auto;">
                {% if recent_activity %}
                    {% for activity in recent_activity %}
                    <div class="activity-item activity-{{ activity.action }} animate__animated animate__fadeInUp" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-{{ 'sign-in-alt text-success' if activity.action == 'joined' else 'sign-out-alt text-danger' }}"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="fw-medium">{{ activity.player }}</div>
                                <small class="text-muted">{{ activity.action }} at {{ activity.time }}</small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-clock fa-2x text-muted mb-2"></i>
                    <p class="text-muted mb-0">No recent activity</p>
                    <small class="text-muted">Player joins and leaves will appear here</small>
                </div>
                {% endif %}
            </div>
        </div>

        <div class="card glass-intense mt-4 animate__animated animate__fadeInRight animate__delay-1s">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-3">
                    <a href="{{ url_for('players') }}" class="btn btn-outline-primary d-flex align-items-center">
                        <i class="fas fa-users me-2"></i>
                        <div class="text-start">
                            <div class="fw-medium">Manage Players</div>
                            <small class="text-muted">View, kick, ban, and manage players</small>
                        </div>
                    </a>
                    <a href="{{ url_for('console') }}" class="btn btn-outline-secondary d-flex align-items-center">
                        <i class="fas fa-terminal me-2"></i>
                        <div class="text-start">
                            <div class="fw-medium">Server Console</div>
                            <small class="text-muted">View logs and execute commands</small>
                        </div>
                    </a>
                    <a href="http://localhost:25580" target="_blank" class="btn btn-outline-info d-flex align-items-center">
                        <i class="fas fa-folder me-2"></i>
                        <div class="text-start">
                            <div class="fw-medium">File Manager</div>
                            <small class="text-muted">Browse and edit server files</small>
                        </div>
                    </a>
                    <button class="btn btn-outline-success d-flex align-items-center" onclick="refreshData(); ToastManager.show('Data refreshed successfully!', 'success')">
                        <i class="fas fa-sync-alt me-2"></i>
                        <div class="text-start">
                            <div class="fw-medium">Refresh Status</div>
                            <small class="text-muted">Update server information</small>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card glass-intense animate__animated animate__fadeInUp animate__delay-2s">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Server Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-4">
                    <div class="col-md-3">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-primary bg-opacity-10 rounded-circle p-2">
                                    <i class="fas fa-server text-primary"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">Server Address</h6>
                                <code class="text-muted">localhost:25565</code>
                                <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('localhost:25565')" data-bs-toggle="tooltip" title="Copy address">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-success bg-opacity-10 rounded-circle p-2">
                                    <i class="fas fa-cube text-success"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">Version</h6>
                                <span class="badge bg-success-subtle text-success">Minecraft 1.21.4</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-warning bg-opacity-10 rounded-circle p-2">
                                    <i class="fas fa-unlock text-warning"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">Mode</h6>
                                <span class="badge bg-warning-subtle text-warning">Offline (Cracked)</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-info bg-opacity-10 rounded-circle p-2">
                                    <i class="fas fa-folder text-info"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">File Browser</h6>
                                <a href="http://localhost:25580" target="_blank" class="text-decoration-none">
                                    <code>localhost:25580</code>
                                    <i class="fas fa-external-link-alt ms-1"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        ToastManager.show('Address copied to clipboard!', 'success');
    }, function(err) {
        ToastManager.show('Failed to copy address', 'error');
    });
}

function refreshActivity() {
    // Add loading animation to the refresh button
    const btn = event.target.closest('button');
    const icon = btn.querySelector('i');
    icon.classList.add('fa-spin');

    setTimeout(() => {
        icon.classList.remove('fa-spin');
        ToastManager.show('Activity refreshed!', 'info');
    }, 1000);
}

// Server Status Auto-Update
let statusUpdateInterval;

async function updateServerStatus() {
    try {
        const response = await fetch('/api/status');
        const status = await response.json();

        // Update server status indicator
        const statusElement = document.getElementById('serverStatus');
        const statusBadge = document.getElementById('serverStatusBadge');
        const statusIcon = document.getElementById('serverStatusIcon');

        if (status.running) {
            if (statusElement) statusElement.textContent = 'Online';
            if (statusBadge) {
                statusBadge.className = 'badge bg-success';
                statusBadge.innerHTML = '<i class="fas fa-check-circle me-1"></i>Online';
            }
            if (statusIcon) {
                statusIcon.className = 'fas fa-server text-success';
            }
        } else {
            if (statusElement) statusElement.textContent = 'Offline';
            if (statusBadge) {
                statusBadge.className = 'badge bg-danger';
                statusBadge.innerHTML = '<i class="fas fa-times-circle me-1"></i>Offline';
            }
            if (statusIcon) {
                statusIcon.className = 'fas fa-server text-danger';
            }
        }

        // Update performance metrics
        const performanceElement = document.getElementById('performanceStatus');
        if (performanceElement) {
            if (status.tps && status.tps !== 'not available' && !status.tps.includes('unavailable')) {
                performanceElement.textContent = status.tps;

                // Update performance color based on TPS or status
                performanceElement.className = 'mb-0 text-muted';

                // Extract TPS value if present
                const tpsMatch = status.tps.match(/TPS:\s*~?(\d+\.?\d*)/);
                if (tpsMatch) {
                    const tpsValue = parseFloat(tpsMatch[1]);
                    if (tpsValue >= 19.5) {
                        performanceElement.className = 'mb-0 text-success fw-bold';
                    } else if (tpsValue >= 18.0) {
                        performanceElement.className = 'mb-0 text-info fw-bold';
                    } else if (tpsValue >= 15.0) {
                        performanceElement.className = 'mb-0 text-warning fw-bold';
                    } else {
                        performanceElement.className = 'mb-0 text-danger fw-bold';
                    }
                } else {
                    // Fallback to response time based coloring
                    if (status.tps.includes('Excellent')) {
                        performanceElement.className = 'mb-0 text-success';
                    } else if (status.tps.includes('Good')) {
                        performanceElement.className = 'mb-0 text-info';
                    } else if (status.tps.includes('Fair')) {
                        performanceElement.className = 'mb-0 text-warning';
                    } else if (status.tps.includes('Slow')) {
                        performanceElement.className = 'mb-0 text-danger';
                    }
                }
            } else {
                performanceElement.textContent = status.running ? 'Calculating...' : 'Server Offline';
                performanceElement.className = 'mb-0 text-muted';
            }
        }

        // Update player count
        const onlineCountElement = document.getElementById('online-count');
        const maxPlayersElement = document.getElementById('max-players');
        if (onlineCountElement && status.online_players) {
            onlineCountElement.textContent = status.online_players.online_count;
        }
        if (maxPlayersElement && status.online_players) {
            maxPlayersElement.textContent = status.online_players.max_players;
        }

        // Update memory usage
        const memoryElement = document.getElementById('memoryStatus');
        if (memoryElement) {
            if (status.memory_usage && status.memory_percent) {
                memoryElement.textContent = `${status.memory_usage} (${status.memory_percent})`;

                // Color code based on memory usage percentage
                const memPercent = parseFloat(status.memory_percent.replace('%', ''));
                if (memPercent > 90) {
                    memoryElement.className = 'mb-0 text-danger';
                } else if (memPercent > 75) {
                    memoryElement.className = 'mb-0 text-warning';
                } else if (memPercent > 50) {
                    memoryElement.className = 'mb-0 text-info';
                } else {
                    memoryElement.className = 'mb-0 text-success';
                }
            } else {
                memoryElement.textContent = '4GB Allocated';
                memoryElement.className = 'mb-0 text-muted';
            }
        }

        // Update CPU usage
        const cpuElement = document.getElementById('cpuStatus');
        if (cpuElement) {
            if (status.cpu_usage) {
                cpuElement.textContent = status.cpu_usage;

                // Color code based on CPU usage percentage
                const cpuPercent = parseFloat(status.cpu_usage.replace('%', ''));
                if (cpuPercent > 90) {
                    cpuElement.className = 'mb-0 text-danger';
                } else if (cpuPercent > 70) {
                    cpuElement.className = 'mb-0 text-warning';
                } else if (cpuPercent > 50) {
                    cpuElement.className = 'mb-0 text-info';
                } else {
                    cpuElement.className = 'mb-0 text-success';
                }
            } else {
                cpuElement.textContent = 'Monitoring...';
                cpuElement.className = 'mb-0 text-muted';
            }
        }

        // Update uptime
        const uptimeElement = document.getElementById('uptimeStatus');
        if (uptimeElement && status.uptime) {
            uptimeElement.textContent = status.uptime;
            uptimeElement.className = 'mb-0 text-success';
        }

    } catch (error) {
        console.error('Error updating server status:', error);

        // Update UI to show error state
        const statusElement = document.getElementById('serverStatus');
        const statusBadge = document.getElementById('serverStatusBadge');
        const performanceElement = document.getElementById('performanceStatus');

        if (statusElement) statusElement.textContent = 'Unknown';
        if (statusBadge) {
            statusBadge.className = 'badge bg-warning';
            statusBadge.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i>Unknown';
        }
        if (performanceElement) performanceElement.textContent = 'Connection Error';
    }
}

// Start auto-updating server status
function startStatusUpdates() {
    updateServerStatus(); // Initial update
    statusUpdateInterval = setInterval(updateServerStatus, 5000); // Update every 5 seconds
}

// Stop auto-updating server status
function stopStatusUpdates() {
    if (statusUpdateInterval) {
        clearInterval(statusUpdateInterval);
        statusUpdateInterval = null;
    }
}

// Initialize status updates when page loads
document.addEventListener('DOMContentLoaded', function() {
    startStatusUpdates();

    // Stop updates when page is hidden (browser tab not active)
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            stopStatusUpdates();
        } else {
            startStatusUpdates();
        }
    });
});

// Clean up when page unloads
window.addEventListener('beforeunload', function() {
    stopStatusUpdates();
});
</script>
{% endblock %}
{% endblock %}
