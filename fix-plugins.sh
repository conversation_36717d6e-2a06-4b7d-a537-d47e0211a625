#!/bin/bash

# Quick Fix for Plugin Download Issues
# This script fixes the Spiget download problem and restarts with direct plugin downloads

echo "🔧 Fixing Plugin Download Issues..."
echo "=================================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# Check Docker
if ! docker info &> /dev/null; then
    log_error "Docker not running!"
    exit 1
fi

# Stop current containers
log_info "Stopping current containers..."
docker-compose down
log_success "Containers stopped"

# Remove the problematic plugin downloads from cache
log_info "Clearing plugin cache..."
docker-compose exec -T mc rm -rf /data/plugins/* 2>/dev/null || true
log_success "Plugin cache cleared"

# Start database first
log_info "Starting database..."
docker-compose up -d db

# Wait for database
log_info "Waiting for database..."
for i in {1..20}; do
    if docker-compose exec -T db mysqladmin ping -h localhost -u root -pminecraft_root_pass --silent 2>/dev/null; then
        break
    fi
    echo "   Database starting... ($i/20)"
    sleep 2
done
log_success "Database ready"

# Start Minecraft server with fixed plugin configuration
log_info "Starting Minecraft server with direct plugin downloads..."
docker-compose up -d mc

# Monitor the startup
log_info "Monitoring server startup..."
echo "📋 Server logs (watching for plugin downloads):"
echo "================================================"

# Follow logs for a bit to see the plugin downloads
timeout 60 docker-compose logs -f mc &
LOGS_PID=$!

# Wait for server to be ready
for i in {1..40}; do
    if docker-compose logs mc | grep -q "Done\|Server startup"; then
        kill $LOGS_PID 2>/dev/null || true
        break
    fi
    sleep 3
done

echo ""
log_success "Server startup process initiated!"

# Check if plugins loaded successfully
echo ""
log_info "Checking plugin status..."
sleep 5

if docker-compose logs mc | grep -q "AuthMe.*enabled\|AuthMe.*loaded"; then
    log_success "AuthMe plugin loaded successfully"
else
    log_warning "AuthMe plugin status unclear - check logs"
fi

if docker-compose logs mc | grep -q "Essentials.*enabled\|Essentials.*loaded"; then
    log_success "EssentialsX plugin loaded successfully"
else
    log_warning "EssentialsX plugin status unclear - check logs"
fi

# Show current status
echo ""
echo "🎮 Current Status:"
echo "=================="
docker-compose ps

echo ""
echo "📝 Next Steps:"
echo "=============="
echo "1. Wait for server to fully start (may take 1-2 minutes)"
echo "2. Connect to localhost:25565"
echo "3. Try: /register testpass testpass"
echo "4. Check for welcome messages"
echo ""
echo "🔧 Useful Commands:"
echo "=================="
echo "View logs:    docker-compose logs -f mc"
echo "Check status: docker-compose ps"
echo "Restart:      docker-compose restart mc"
echo ""

# Ask if user wants to continue watching logs
read -p "📋 Continue watching server logs? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "📋 Following server logs (Press Ctrl+C to exit):"
    docker-compose logs -f mc
fi
