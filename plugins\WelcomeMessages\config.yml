# Welcome Messages Plugin Configuration
# Custom welcome messages for new and returning players

# Enable welcome messages
enabled: true

# Welcome message for first-time players
first-join:
  enabled: true
  title:
    enabled: true
    title: '&6&lWelcome!'
    subtitle: '&eThank you for joining our server!'
    fade-in: 20
    stay: 60
    fade-out: 20
  chat-messages:
    - ''
    - '&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬'
    - '&6&l                    WELCOME TO OUR SERVER!'
    - ''
    - '&e&l                    Hello, &a{player}&e&l!'
    - ''
    - '&7                 This is your first time joining our server.'
    - '&7                 To get started, you need to register an account:'
    - ''
    - '&a&l                 /register <password> <confirmPassword>'
    - ''
    - '&7                 After registering, you can login with:'
    - '&a&l                 /login <password>'
    - ''
    - '&7                 Need help? Type &a/help &7or ask in chat!'
    - ''
    - '&6                 Website: &fYourWebsite.com'
    - '&6                 Discord: &fYourDiscord'
    - ''
    - '&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬'
    - ''
  sound:
    enabled: true
    sound: 'ENTITY_PLAYER_LEVELUP'
    volume: 1.0
    pitch: 1.0
  delay: 20  # Delay in ticks (1 second)

# Welcome message for returning players
returning-join:
  enabled: true
  title:
    enabled: true
    title: '&a&lWelcome Back!'
    subtitle: '&eGood to see you again, &a{player}&e!'
    fade-in: 20
    stay: 40
    fade-out: 20
  chat-messages:
    - ''
    - '&a&l» &7Welcome back, &a{player}&7!'
    - '&7  Please login with: &a/login <password>'
    - ''
  sound:
    enabled: true
    sound: 'ENTITY_EXPERIENCE_ORB_PICKUP'
    volume: 0.8
    pitch: 1.2
  delay: 10  # Delay in ticks (0.5 seconds)

# Login success message
login-success:
  enabled: true
  title:
    enabled: true
    title: '&2&lLogin Successful!'
    subtitle: '&aWelcome to the server!'
    fade-in: 10
    stay: 30
    fade-out: 10
  chat-messages:
    - '&2&l✓ &aSuccessfully logged in! Enjoy your stay!'
  sound:
    enabled: true
    sound: 'ENTITY_PLAYER_LEVELUP'
    volume: 0.7
    pitch: 1.5
  delay: 5

# Registration success message
register-success:
  enabled: true
  title:
    enabled: true
    title: '&2&lRegistration Complete!'
    subtitle: '&aYour account has been created!'
    fade-in: 10
    stay: 40
    fade-out: 10
  chat-messages:
    - ''
    - '&2&l✓ &aRegistration successful!'
    - '&7  Your account has been created and you are now logged in.'
    - '&7  Remember your password for future logins!'
    - ''
    - '&6  Type &a/help &6to see available commands.'
    - '&6  Type &a/rules &6to read the server rules.'
    - ''
  sound:
    enabled: true
    sound: 'UI_TOAST_CHALLENGE_COMPLETE'
    volume: 1.0
    pitch: 1.0
  delay: 10

# MOTD (Message of the Day) shown periodically
motd:
  enabled: true
  interval: 300  # Show every 5 minutes (in seconds)
  messages:
    - '&6&l» &eRemember to follow the server rules!'
    - '&6&l» &eNeed help? Type &a/help &efor assistance!'
    - '&6&l» &eJoin our Discord: &fYourDiscord'
    - '&6&l» &eVisit our website: &fYourWebsite.com'
    - '&6&l» &eHave fun and enjoy the game!'

# Server information messages
info-messages:
  rules:
    - ''
    - '&6&l                    SERVER RULES'
    - ''
    - '&71. &eBe respectful to all players'
    - '&72. &eNo griefing, stealing, or cheating'
    - '&73. &eNo inappropriate language or behavior'
    - '&74. &eNo advertising other servers'
    - '&75. &eHave fun and enjoy the game!'
    - ''
    - '&cBreaking these rules may result in punishment!'
    - ''
  
  help:
    - ''
    - '&6&l                    HELPFUL COMMANDS'
    - ''
    - '&a/login <password> &7- Login to your account'
    - '&a/register <password> <confirm> &7- Register a new account'
    - '&a/changepassword <old> <new> &7- Change your password'
    - '&a/spawn &7- Teleport to spawn'
    - '&a/sethome &7- Set your home location'
    - '&a/home &7- Teleport to your home'
    - '&a/tpa <player> &7- Request to teleport to a player'
    - '&a/msg <player> <message> &7- Send a private message'
    - '&a/rules &7- View server rules'
    - ''

# Placeholders
placeholders:
  player: '{player}'
  displayname: '{displayname}'
  world: '{world}'
  x: '{x}'
  y: '{y}'
  z: '{z}'
  time: '{time}'
  date: '{date}'
  online: '{online}'
  max-players: '{max-players}'

# Advanced settings
settings:
  # Check for first join using player data files
  check-first-join: true
  # Delay before showing messages (in ticks)
  message-delay: 20
  # Enable debug mode
  debug: false
  # Use permission-based messages
  use-permissions: false
  # Permission for receiving welcome messages
  permission: 'welcomemessages.receive'
